import request from "@/utils/request";

/**
 * 支付宝与邦道账单-银行流水汇总API接口
 * 提供银行流水汇总的查询、导出、核对等功能
 */
export default {
  /**
   * 分页查询银行流水汇总
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum] - 页码
   * @param {number} [data.pageSize] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.remark] - 备注pid
   * @param {string} [data.counterpartyBankName] - 银行户名
   * @param {string} [data.matchedBillMonthStart] - 账单月份开始
   * @param {string} [data.matchedBillMonthEnd] - 账单月份结束
   * @param {string} [data.startFlowImportDay] - 导入时间开始
   * @param {string} [data.endFlowImportDay] - 导入时间结束
   * @param {string} [data.diffResult] - 比对结果
   * @param {string} [data.fieldName] - 查询字段
   * @param {string} [data.fieldValue] - 查询字段值
   * @returns {Promise<Object>} 返回分页查询结果
   */
  list(data) {
    return request({
      url: "/st/lifePay/bankFlow/summary/queryPage",
      method: "post",
      data: data,
    });
  },

  /**
   * 导出银行流水汇总Excel
   * @param {Object} data - 导出参数，参数结构同查询接口
   * @returns {Promise<Object>} 返回导出结果
   */
  export(data) {
    return request({
      url: "/st/lifePay/bankFlow/summary/exportExcel",
      method: "post",
      data: data,
    });
  },

  /**
   * 与支付宝核对
   * @param {Object} data - 核对参数
   * @param {Array<number>} data.idList - 主键id列表，取列表返回的bankFlowSummaryId
   * @param {boolean} [data.allPageFlag=false] - 全部页标识
   * @returns {Promise<Object>} 返回核对结果
   * @returns {Object} returns.data - 核对结果数据
   * @returns {number} returns.data.sameNum - 比对一致数量
   * @returns {number} returns.data.diffNum - 比对不一致数量
   */
  checkWithAlipay(data) {
    return request({
      url: "/st/lifePay/bankFlow/summary/checkWithAlipay",
      method: "post",
      data: data,
    });
  },

  /**
   * 查询银行户名下拉列表
   * @returns {Promise<Object>} 返回下拉列表数据
   * @returns {Array<string>} returns.data - 银行户名列表
   */
  getDropLists(data) {
    return request({
      url: "/st/lifePay/bankFlow/summary/queryDistinctValue",
      method: "post",
      data,
    });
  },
};
