import request from "@/utils/request";

//公共邮箱
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/publicEmail/queryList",
      method: "post",
      data: data,
    });
  },
  // 回复邮件
  replyEmail(data) {
    return request({
      url: "/publicEmail/replayEmails",
      method: "post",
      data: data,
    });
  },
  // 获取邮件发送记录
  getEmailSendRecords(data) {
    return request({
      url: "/publicEmail/mailSendRecord",
      method: "get",
      params: data,
    });
  },
  //导出
  export(data) {
    return request({
      url: "/publicEmail/export",
      method: "post",
      data: data,
    });
  },
  // 作废
  cancel(data) {
    return request({
      url: "/publicEmail/cancelOrder",
      method: "post",
      data: data,
    });
  },
  // 接单
  accept(data) {
    return request({
      url: "/publicEmail/receiveOrder",
      method: "post",
      data: data,
    });
  },
  // 批量接单
  batchAccept(data) {
    return request({
      url: "/publicEmail/batchReceiveOrder",
      method: "post",
      data: data,
    });
  },
  // 备注
  remark(data) {
    return request({
      url: "/publicEmail/remark",
      method: "post",
      data: data,
    });
  },
  // 活动类别
  activityType(data) {
    return request({
      url: "/publicEmail/activityType",
      method: "post",
      data: data,
    });
  },
  //保存配置
  saveConfig(data) {
    return request({
      url: "/publicEmail/saveMailDataConfig",
      method: "post",
      data: data,
    });
  },
  //复核
  saveRecheck(data) {
    return request({
      url: "/publicEmail/saveMailDataReview",
      method: "post",
      data: data,
    });
  },
  //配置结果详情
  getConfigDetail(data) {
    return request({
      url: "/publicEmail/configDetail",
      method: "get",
      params: data,
    });
  },
  //复核结果详情
  getRecheckDetail(data) {
    return request({
      url: "/publicEmail/reviewDetail",
      method: "get",
      params: data,
    });
  },
  //场站差异明细-提交
  difference(data) {
    return request({
      url: "/publicEmail/reviewStationUpdate",
      method: "post",
      data: data,
    });
  },
  //详情-邮件内容
  queryMailDetail(data) {
    return request({
      url: "/publicEmail/basicDetail",
      method: "post",
      data: data,
    });
  },
  //详情-操作记录
  queryRecordList(data) {
    return request({
      url: "/orderRecord/getByBusinessId",
      method: "get",
      params: data,
    });
  },
  // 获取已配置的运营商
  getOperatorList(data) {
    return request({
      url: "/publicEmail/operatorList",
      method: "get",
      params: data,
    });
  },
  //场站名称分页模糊搜索
  getStationOptions(data) {
    return request({
      url: "/publicEmail/stationList",
      method: "post",
      data: data,
    });
  },

  //配置结果表格
  getResultList(data) {
    return request({
      url: "/publicEmail/reviewStationList",
      method: "post",
      data: data,
    });
  },
  //配置结果-导出
  exportResult(data) {
    return request({
      url: "publicEmail/reviewStationExport",
      method: "post",
      data: data,
    });
  },

  // =================== 邮件数据源配置相关接口 ===================

  // 获取邮件数据源配置分页列表
  getMailSourceConfigList(data) {
    return request({
      url: "/mail/source/config/list",
      method: "post",
      data: data,
    });
  },

  // 保存邮件数据源配置
  add(data) {
    return request({
      url: "/mail/source/config/save",
      method: "post",
      data: data,
    });
  },

  // 获取邮件数据源配置详情
  getMailSourceConfigDetail(params) {
    return request({
      url: "/mail/source/config/detail",
      method: "get",
      params: params,
    });
  },

  // 修改邮件数据源配置
  update(data) {
    return request({
      url: "/mail/source/config/updateMailConfig",
      method: "post",
      data: data,
    });
  },

  // 修改邮件数据源配置状态
  updateMailSourceConfigStatus(params) {
    return request({
      url: "/mail/source/config/updateStatus",
      method: "get",
      params: params,
    });
  },
  // 邮箱来源列表
  getMailSource(params) {
    return request({
      url: "/publicEmail/mailSource",
      method: "get",
      params: params,
    });
  },

  // 保存邮件数据源数据配置
  saveDataConfig(data) {
    return request({
      url: "/mail/source/config/dataConfig",
      method: "post",
      data: data,
    });
  },
  //获取邮箱发件人下拉选项
  getEmailOptions(params) {
    return request({
      url: "/publicEmail/mailAddress",
      method: "get",
      params: params,
    });
  },
};
