<template>
  <div class="demo-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>简化版颜色应用测试</span>
      </div>
      
      <div class="demo-content">
        <h3>测试说明</h3>
        <p>这是一个简化版的颜色应用功能，只在光标所在行应用颜色，避免影响其他行。</p>
        
        <h4>测试步骤：</h4>
        <ol>
          <li>确认编辑器显示三行蓝色文字</li>
          <li>点击第二行"（公司名称手动自定义）"</li>
          <li>删除这些文字并输入"阿里巴巴公司"</li>
          <li>等待0.5秒，观察文字是否变为蓝色</li>
          <li>测试光标位置是否正常</li>
        </ol>

        <div class="editor-wrapper">
          <h4>富文本编辑器：</h4>
          <Editor
            v-model="content"
            ref="testEditor"
            class="demo-editor"
            :lineColorConfig="lineColorConfig"
          />
        </div>

        <div class="content-preview">
          <h4>当前内容预览：</h4>
          <div class="preview-box" v-html="content"></div>
        </div>

        <div class="actions">
          <el-button @click="resetContent" type="primary">重置内容</el-button>
          <el-button @click="testManualColor" type="success">手动应用颜色</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import Editor from "@/components/Editor/index.vue";

export default {
  name: "SimpleColorTest",
  components: { Editor },
  data() {
    return {
      content: `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`,
      lineColorConfig: [
        { targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" }
      ]
    };
  },
  methods: {
    resetContent() {
      this.content = `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`;
      this.$message.success("内容已重置为三行格式");
    },
    
    testManualColor() {
      if (this.$refs.testEditor && this.$refs.testEditor.applyColorToCurrentLine) {
        this.$refs.testEditor.applyColorToCurrentLine();
        this.$message.success("手动应用颜色完成");
      } else {
        this.$message.error("编辑器实例未找到");
      }
    }
  }
};
</script>

<style lang="less" scoped>
.demo-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.demo-content {
  h3 {
    color: #333;
    margin-bottom: 10px;
  }
  
  h4 {
    color: #666;
    margin: 20px 0 10px 0;
  }
  
  p {
    line-height: 1.6;
    color: #666;
  }
  
  ol {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}

.editor-wrapper {
  margin: 20px 0;
}

.demo-editor {
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  
  /deep/ .ql-container {
    max-height: 250px;
    height: 250px;
  }
}

.content-preview {
  margin: 20px 0;
  
  .preview-box {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
    min-height: 100px;
    
    p {
      margin: 8px 0;
    }
  }
}

.actions {
  text-align: center;
  margin-top: 30px;
  
  .el-button + .el-button {
    margin-left: 10px;
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
