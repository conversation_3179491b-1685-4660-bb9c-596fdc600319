<template>
  <div class="demo-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>新颜色应用功能测试</span>
      </div>
      
      <div class="demo-content">
        <h3>功能说明</h3>
        <p>使用 Quill 原生 API 实现的颜色应用功能，应该不会影响光标位置。</p>
        
        <h4>测试步骤：</h4>
        <ol>
          <li>观察编辑器中的三行蓝色文字</li>
          <li>点击第二行"（公司名称手动自定义）"</li>
          <li>删除这些文字并输入新内容，如"阿里巴巴公司"</li>
          <li>观察新输入的文字是否自动变为蓝色</li>
          <li>测试光标位置是否正常</li>
        </ol>

        <div class="editor-wrapper">
          <h4>富文本编辑器：</h4>
          <Editor
            v-model="content"
            ref="testEditor"
            class="demo-editor"
            :lineColorConfig="lineColorConfig"
          />
        </div>

        <div class="content-preview">
          <h4>当前内容预览：</h4>
          <div class="preview-box" v-html="content"></div>
        </div>

        <div class="debug-info">
          <h4>调试信息：</h4>
          <p><strong>纯文本内容：</strong></p>
          <pre>{{ debugText }}</pre>
          <p><strong>行数：</strong> {{ debugLines.length }}</p>
          <p><strong>各行内容：</strong></p>
          <ul>
            <li v-for="(line, index) in debugLines" :key="index">
              第{{ index + 1 }}行: "{{ line }}"
            </li>
          </ul>
        </div>

        <div class="actions">
          <el-button @click="resetContent" type="primary">重置内容</el-button>
          <el-button @click="manualApplyColor" type="success">手动应用颜色</el-button>
          <el-button @click="getDebugInfo" type="info">获取调试信息</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import Editor from "@/components/Editor/index.vue";

export default {
  name: "NewColorTest",
  components: { Editor },
  data() {
    return {
      content: `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`,
      lineColorConfig: [
        { targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" }
      ],
      debugText: "",
      debugLines: []
    };
  },
  methods: {
    resetContent() {
      this.content = `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`;
      this.$message.success("内容已重置");
      this.getDebugInfo();
    },
    
    manualApplyColor() {
      if (this.$refs.testEditor && this.$refs.testEditor.applyLineColorAfterTextChange) {
        this.$refs.testEditor.applyLineColorAfterTextChange();
        this.$message.success("手动应用颜色完成");
        this.getDebugInfo();
      } else {
        this.$message.error("编辑器实例未找到");
      }
    },
    
    getDebugInfo() {
      if (this.$refs.testEditor && this.$refs.testEditor.quill) {
        this.debugText = this.$refs.testEditor.quill.getText();
        this.debugLines = this.debugText.split('\n');
        this.$message.info("调试信息已更新");
      }
    }
  },
  
  mounted() {
    this.$nextTick(() => {
      this.getDebugInfo();
    });
  }
};
</script>

<style lang="less" scoped>
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-content {
  h3 {
    color: #333;
    margin-bottom: 10px;
  }
  
  h4 {
    color: #666;
    margin: 20px 0 10px 0;
  }
  
  p {
    line-height: 1.6;
    color: #666;
  }
  
  ol {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}

.editor-wrapper {
  margin: 20px 0;
}

.demo-editor {
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  
  /deep/ .ql-container {
    max-height: 250px;
    height: 250px;
  }
}

.content-preview {
  margin: 20px 0;
  
  .preview-box {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
    min-height: 100px;
    
    p {
      margin: 8px 0;
    }
  }
}

.debug-info {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  
  pre {
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-size: 12px;
    white-space: pre-wrap;
  }
  
  ul {
    margin: 10px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 5px;
      font-family: monospace;
    }
  }
}

.actions {
  text-align: center;
  margin-top: 30px;
  
  .el-button + .el-button {
    margin-left: 10px;
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
