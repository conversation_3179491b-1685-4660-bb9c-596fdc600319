<template>
  <div class="demo-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>富文本编辑器行颜色应用功能演示</span>
      </div>
      
      <div class="demo-content">
        <h3>功能说明</h3>
        <p>此演示展示了富文本编辑器在特定行粘贴内容时自动应用颜色的功能。</p>
        
        <h4>测试步骤：</h4>
        <ol>
          <li>观察编辑器中的蓝色文字"（公司名称手动自定义）"</li>
          <li>复制下方的示例文字（如"阿里巴巴公司"）</li>
          <li>选中蓝色文字"（公司名称手动自定义）"并粘贴</li>
          <li>观察粘贴后的文字是否保持蓝色</li>
        </ol>

        <div class="sample-text">
          <h4>示例文字（可复制）：</h4>
          <div class="text-box">
            <p>阿里巴巴公司</p>
            <p>腾讯科技有限公司</p>
            <p>百度在线网络技术（北京）有限公司</p>
          </div>
        </div>

        <div class="editor-wrapper">
          <h4>富文本编辑器：</h4>
          <Editor
            v-model="content"
            ref="demoEditor"
            class="demo-editor"
            :lineColorConfig="lineColorConfig"
          />
        </div>

        <div class="content-preview">
          <h4>当前内容预览：</h4>
          <div class="preview-box" v-html="content"></div>
        </div>

        <div class="html-source">
          <h4>HTML源码：</h4>
          <pre class="source-code">{{ content }}</pre>
        </div>

        <div class="actions">
          <el-button @click="resetContent" type="primary">重置内容</el-button>
          <el-button @click="testColorApplication" type="success">手动测试颜色应用</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import Editor from "@/components/Editor/index.vue";

export default {
  name: "LineColorDemo",
  components: { Editor },
  data() {
    return {
      content: `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`,
      // 特定行颜色配置
      lineColorConfig: [
        { targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" }
      ]
    };
  },
  methods: {
    resetContent() {
      this.content = `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`;
      this.$message.success("内容已重置");
    },
    
    testColorApplication() {
      // 手动触发颜色应用功能
      if (this.$refs.demoEditor && this.$refs.demoEditor.applyLineColorAfterPaste) {
        this.$refs.demoEditor.applyLineColorAfterPaste();
        this.$message.success("颜色应用功能已执行");
      } else {
        this.$message.error("编辑器实例未找到或方法不存在");
      }
    }
  }
};
</script>

<style lang="less" scoped>
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-content {
  h3 {
    color: #333;
    margin-bottom: 10px;
  }
  
  h4 {
    color: #666;
    margin: 20px 0 10px 0;
  }
  
  p {
    line-height: 1.6;
    color: #666;
  }
  
  ol {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}

.sample-text {
  margin: 20px 0;
  
  .text-box {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    background-color: #f8f8f8;
    
    p {
      margin: 8px 0;
      cursor: pointer;
      
      &:hover {
        background-color: #eaeaea;
      }
    }
  }
}

.editor-wrapper {
  margin: 20px 0;
}

.demo-editor {
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  
  /deep/ .ql-container {
    max-height: 250px;
    height: 250px;
  }
}

.content-preview {
  margin: 20px 0;
  
  .preview-box {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
    min-height: 100px;
    
    p {
      margin: 8px 0;
    }
  }
}

.html-source {
  margin: 20px 0;
  
  .source-code {
    background-color: #f8f8f8;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', Consolas, monospace;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 200px;
    overflow-y: auto;
  }
}

.actions {
  text-align: center;
  margin-top: 30px;
  
  .el-button + .el-button {
    margin-left: 10px;
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
