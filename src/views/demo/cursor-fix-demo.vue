<template>
  <div class="demo-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>光标位置修复演示</span>
      </div>
      
      <div class="demo-content">
        <h3>问题说明</h3>
        <p>测试光标位置是否正常，以及三行文本是否保持正确的格式。</p>
        
        <h4>测试步骤：</h4>
        <ol>
          <li>观察编辑器中是否有三行蓝色文字</li>
          <li>点击编辑器中的任意位置，光标应该能正常定位</li>
          <li>在第二行"（公司名称手动自定义）"中输入内容</li>
          <li>观察光标是否保持在正确位置</li>
        </ol>

        <div class="editor-wrapper">
          <h4>富文本编辑器：</h4>
          <Editor
            v-model="content"
            ref="demoEditor"
            class="demo-editor"
            :lineColorConfig="lineColorConfig"
          />
        </div>

        <div class="content-preview">
          <h4>当前内容预览：</h4>
          <div class="preview-box" v-html="content"></div>
        </div>

        <div class="html-source">
          <h4>HTML源码：</h4>
          <pre class="source-code">{{ content }}</pre>
        </div>

        <div class="actions">
          <el-button @click="resetContent" type="primary">重置内容</el-button>
          <el-button @click="disableColorFeature" type="warning">禁用颜色功能</el-button>
          <el-button @click="enableColorFeature" type="success">启用颜色功能</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import Editor from "@/components/Editor/index.vue";

export default {
  name: "CursorFixDemo",
  components: { Editor },
  data() {
    return {
      content: `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`,
      // 特定行颜色配置
      lineColorConfig: [
        { targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" }
      ]
    };
  },
  methods: {
    resetContent() {
      this.content = `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`;
      this.$message.success("内容已重置为三行格式");
    },
    
    disableColorFeature() {
      this.lineColorConfig = [];
      this.$message.warning("颜色功能已禁用，可以测试光标是否正常");
    },
    
    enableColorFeature() {
      this.lineColorConfig = [
        { targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" }
      ];
      this.$message.success("颜色功能已启用");
    }
  }
};
</script>

<style lang="less" scoped>
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-content {
  h3 {
    color: #333;
    margin-bottom: 10px;
  }
  
  h4 {
    color: #666;
    margin: 20px 0 10px 0;
  }
  
  p {
    line-height: 1.6;
    color: #666;
  }
  
  ol {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}

.editor-wrapper {
  margin: 20px 0;
}

.demo-editor {
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  
  /deep/ .ql-container {
    max-height: 250px;
    height: 250px;
  }
}

.content-preview {
  margin: 20px 0;
  
  .preview-box {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
    min-height: 100px;
    
    p {
      margin: 8px 0;
    }
  }
}

.html-source {
  margin: 20px 0;
  
  .source-code {
    background-color: #f8f8f8;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', Consolas, monospace;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 200px;
    overflow-y: auto;
  }
}

.actions {
  text-align: center;
  margin-top: 30px;
  
  .el-button + .el-button {
    margin-left: 10px;
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
