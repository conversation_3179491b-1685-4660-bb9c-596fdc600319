<template>
  <div class="test-editor-container">
    <h3>富文本编辑器颜色保留功能测试</h3>
    
    <div class="test-section">
      <h4>测试说明：</h4>
      <ol>
        <li>编辑器中已预设蓝色文字"（公司名称手动自定义）"</li>
        <li>尝试复制任意文字并粘贴到该行，观察颜色是否保持为蓝色</li>
        <li>测试其他蓝色文字"致"和"确认，感谢支持！"的颜色保留</li>
      </ol>
    </div>

    <div class="editor-section">
      <h4>富文本编辑器：</h4>
      <Editor
        v-model="content"
        ref="testEditor"
        class="test-editor"
        :preserveColorTexts="preserveColorTexts"
      />
    </div>

    <div class="content-display">
      <h4>当前内容（HTML）：</h4>
      <pre>{{ content }}</pre>
    </div>

    <div class="actions">
      <el-button @click="resetContent">重置内容</el-button>
      <el-button @click="addTestText">添加测试文字</el-button>
      <el-button type="primary" @click="testColorPreservation">测试颜色保留</el-button>
    </div>
  </div>
</template>

<script>
import Editor from "@/components/Editor/index.vue";

export default {
  name: "TestEditorColorPreservation",
  components: { Editor },
  data() {
    return {
      content: `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`,
      // 需要保留颜色的文字配置
      preserveColorTexts: [
        { text: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" },
        { text: "致", color: "rgb(0, 0, 128)" },
        { text: "确认，感谢支持！", color: "rgb(0, 0, 128)" }
      ]
    };
  },
  methods: {
    resetContent() {
      this.content = `<p><span style="color: rgb(0, 0, 128);">致</span></p><p><span style="color: rgb(0, 0, 128);">（公司名称手动自定义）</span></p><p><span style="color: rgb(0, 0, 128);">确认，感谢支持！</span></p>`;
    },
    
    addTestText() {
      this.content += `<p>这是一段普通文字，用于测试粘贴功能</p>`;
    },
    
    testColorPreservation() {
      // 模拟粘贴操作后的颜色保留测试
      if (this.$refs.testEditor && this.$refs.testEditor.preserveTargetTextColors) {
        this.$refs.testEditor.preserveTargetTextColors();
        this.$message.success("颜色保留测试完成");
      } else {
        this.$message.error("编辑器实例未找到");
      }
    }
  }
};
</script>

<style lang="less" scoped>
.test-editor-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
  
  h4 {
    margin-top: 0;
    color: #333;
  }
  
  ol {
    margin: 10px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 5px;
      line-height: 1.5;
    }
  }
}

.editor-section {
  margin-bottom: 20px;
  
  h4 {
    margin-bottom: 10px;
    color: #333;
  }
}

.test-editor {
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  
  /deep/ .ql-container {
    max-height: 250px;
    height: 250px;
  }
}

.content-display {
  margin-bottom: 20px;
  
  h4 {
    margin-bottom: 10px;
    color: #333;
  }
  
  pre {
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

.actions {
  text-align: center;
  
  .el-button + .el-button {
    margin-left: 10px;
  }
}
</style>
