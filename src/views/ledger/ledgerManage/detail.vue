//工单详情
<template>
  <div class="app-container">
    <div class="box-title">
      <h2>工单详情</h2>
      <el-dropdown @command="(command) => handleCommand(command, basicForm)">
        <el-button type="text" size="large">
          快捷操作<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="(item, index) in moreBtnList"
              :command="item.command"
              :key="index"
              v-show="item.show(basicForm)"
            >
              <el-button type="text" size="medium">
                {{ item.title }}
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="base">
        <BaseInfo ref="base"></BaseInfo>
      </el-tab-pane>
      <el-tab-pane
        label="处理信息"
        name="handle"
        v-if="orderId && orderStatus !== '草稿'"
      >
        <HandleInfo ref="handle"></HandleInfo>
      </el-tab-pane>
      <el-tab-pane
        label="时效统计"
        name="time"
        v-if="orderId && orderStatus !== '草稿'"
      >
        <TimeInfo ref="time"></TimeInfo>
      </el-tab-pane>
      <el-tab-pane label="操作记录" name="record">
        <RecordInfo ref="record"></RecordInfo>
      </el-tab-pane>
    </el-tabs>
    <el-drawer
      title="处理工单"
      :visible.sync="handleVisible"
      @close="handleVisible = false"
      size="70%"
    >
      <Handle
        :flowKeyProp="basicForm.flowKey"
        :orderIdProp="basicForm.orderId"
        :orderNoProp="basicForm.orderNo"
        @submit="closeHandleDrawer"
        ref="handleDrawer"
      ></Handle>
    </el-drawer>
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      :config="modalConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
      labelWidth="130px"
    >
      <template #deptTree="{item,params}">
        <treeselect
          v-model="params.deptId"
          :options="deptOptions"
          placeholder="请选择归属部门"
          @select="handleNodeClick"
          :beforeClearAll="beforeClearAll"
          :default-expand-level="1"
          :normalizer="normalizer"
        />
      </template>
    </BaseFormModal>
    <AddOrderDrawer ref="addOrderDrawer" @success="loadData"></AddOrderDrawer>
    <CheckOrderDrawer
      ref="checkOrderDrawer"
      @success="loadData"
    ></CheckOrderDrawer>
    <CheckProcessDrawer ref="checkProcessDrawer"></CheckProcessDrawer>
  </div>
</template>

<script>
import BaseInfo from "./detailComponents/baseInfo.vue";
import HandleInfo from "./detailComponents/handleInfo.vue";
import TimeInfo from "./detailComponents/timeInfo.vue";
import RecordInfo from "./detailComponents/recordInfo.vue";
import api from "@/api/ledger/index.js";
import checkPermission from "@/utils/permission.js";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import { mapGetters } from "vuex";
import Handle from "./handle.vue";
import { initParams } from "@/utils/buse.js";
import { listAllUser, listDept } from "@/api/common.js";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AddOrderDrawer from "./components/addOrderDrawer.vue";
import CheckOrderDrawer from "./components/checkOrderDrawer.vue";
import CheckProcessDrawer from "./components/checkProcessDrawer.vue";

export default {
  components: {
    BaseInfo,
    HandleInfo,
    TimeInfo,
    RecordInfo,
    Handle,
    BaseFormModal,
    Treeselect,
    AddOrderDrawer,
    CheckOrderDrawer,
    CheckProcessDrawer,
  },
  data() {
    return {
      deptOptions: [],
      userOption: [],
      handleVisible: false,
      activeName: "base",
      orderId: "",
      orderNo: "",
      orderStatus: "",
      basicForm: {},
      operationType: "transfer",
      moreBtnList: [
        {
          title: "接单",
          command: "acceptOrder",
          clickFn: (row) => {
            return this.handleAcceptOrder(row);
          },
          show: (row) => {
            //处理中
            const condition1 = ["处理中"].includes(row.orderStatus);
            //接单人为空
            const condition2 = !row.takeOrderUser;
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:acceptOrder"])
            );
          },
        },
        {
          title: "编辑",
          command: "edit",
          clickFn: (row) => {
            this.JumpToEdit(row, "edit");
          },
          show: (row) => {
            //草稿
            const condition1 = row.orderStatus == "草稿";
            //系统管理员/工单创建人
            const condition2 =
              this.isAdminRole || this.userId === row.createUser;
            return (
              condition1 && condition2 && checkPermission(["ledger:list:edit"])
            );
          },
        },
        {
          title: "处理",
          command: "handle",
          clickFn: (row) => {
            this.handleDrawer(row);
          },
          show: (row) => {
            //处理中
            const condition1 = row.orderStatus == "处理中";
            //系统管理员/当前节点的处理人 / 处理组/ 处理角色
            const condition2 =
              this.isAdminRole || row.operateUserIds?.includes(this.userId);
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:handle"])
            );
          },
        },
        {
          title: "转派",
          command: "transfer",
          clickFn: (row) => {
            this.handleTransfer(row);
          },
          show: (row) => {
            //处理中&&加单审核状态非待审核
            const condition1 =
              row.orderStatus == "处理中" && row.auditResult != "0";
            //系统管理员/当前节点的处理人 / 处理组/ 处理角色
            const condition2 =
              this.isAdminRole || row.operateUserIds?.includes(this.userId);
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:transfer"])
            );
          },
        },
        {
          title: "加单",
          command: "addOrder",
          clickFn: (row) => {
            this.handleAddOrder(row);
          },
          show: (row) => {
            //处理中&&加单审核状态不是待审核
            const condition1 =
              ["处理中"].includes(row.orderStatus) && row.auditResult != 0;
            //已完成&&加单审核状态不是待审核/审核不通过
            const condition3 =
              ["已完成"].includes(row.orderStatus) &&
              !["0", "2"].includes(row.auditResult);
            //系统管理员/当前节点的处理人 / 处理组/ 处理角色
            const condition2 =
              this.isAdminRole || row.operateUserIds?.includes(this.userId);
            return (
              (condition1 || condition3) &&
              condition2 &&
              checkPermission(["ledger:list:addOrder"])
            );
          },
        },
        {
          title: "完成",
          command: "end",
          clickFn: (row) => {
            this.handleEnd(row);
          },
          show: (row) => {
            //处理中&&加单审核状态不是待审核
            const condition1 =
              ["处理中"].includes(row.orderStatus) && row.auditResult != 0;
            //系统管理员/当前节点的处理人 / 处理组/ 处理角色
            const condition2 =
              this.isAdminRole || row.operateUserIds?.includes(this.userId);
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:endOrder"])
            );
          },
        },
        {
          title: "备注",
          command: "remark",
          clickFn: (row) => {
            this.handleRemark(row);
          },
          show: (row) => {
            return checkPermission(["ledger:list:remark"]);
          },
        },
        {
          title: "审核",
          command: "check",
          clickFn: (row) => {
            this.handleCheck(row);
          },
          show: (row) => {
            //已完成&&加单审核状态为待审核/审核不通过
            const condition1 =
              ["已完成"].includes(row.orderStatus) &&
              ["0", "2"].includes(row.auditResult);
            //处理中&&加单审核状态为待审核
            const condition2 =
              row.orderStatus == "处理中" && row.auditResult == "0";
            //系统管理员/加单审核人
            const condition3 =
              this.isAdminRole || row.auditUser === this.userId;
            return (
              (condition1 || condition2) &&
              condition3 &&
              checkPermission(["ledger:list:check"])
            );
          },
        },
        // {
        //   title: "关联申请单号",
        //   command: "associate",
        //   clickFn: (row) => {
        //     this.handleAssociate(row);
        //   },
        //   show: (row) => {
        //     return checkPermission(["ledger:list:associate"]);
        //   },
        // },
        {
          title: "查看审批进度",
          command: "process",
          clickFn: (row) => {
            this.handleProcess(row);
          },
          show: (row) => {
            return checkPermission(["ledger:list:process"]);
          },
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    //系统管理员
    isAdminRole() {
      return this.roles.includes("admin");
    },
    modalConfig() {
      const form = {
        //转派
        transfer: {
          modalTitle: "转派",
          formConfig: [
            {
              field: "deptId",
              element: "slot",
              slotName: "deptTree",
              title: "选择部门",
            },
            {
              field: "targetUserId",
              element: "el-select",
              title: "选择人员",
              props: {
                options: this.userOption,
                filterable: true,
                placeholder: "请输入或选择人员",
              },
              rules: [{ required: true, message: "请选择人员" }],
            },
            {
              field: "transferRemark",
              element: "el-input",
              title: "转派原因",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "请输入具体的原因描述，500个字符以内",
              },
              rules: [
                {
                  required: true,
                  message: "请输入转派原因",
                  trigger: "change",
                },
              ],
            },
          ],
        },
        //完成
        end: {
          modalTitle: "手动完成工单",
          formConfig: [
            {
              field: "finishType",
              title: "数据处理方式",
              element: "el-radio-group",
              props: {
                options: [
                  { value: 1, label: "将当前节点变成已处理" },
                  { value: 2, label: "将所有节点变成已处理" },
                ],
              },
              defaultValue: 1,
              rules: [{ required: true, message: "请选择数据处理方式！" }],
            },
            {
              field: "finishRemark",
              element: "el-input",
              title: "完成原因",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "请输入具体的描述，500个字符以内",
              },
              // rules: [{ required: true, message: "完成原因不能为空！" }],
            },
          ],
        },
        //备注
        remark: {
          modalTitle: "备注",
          formConfig: [
            {
              field: "remark",
              element: "el-input",
              title: "备注信息",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "请输入具体的描述，500个字符以内",
              },
              rules: [{ required: true, message: "备注不能为空！" }],
            },
            {
              field: "targetUserId",
              element: "el-radio-group",
              title: "工单属性",
              props: {
                options: [
                  { value: 0, label: "标准需求" },
                  { value: 1, label: "非标需求" },
                ],
              },
              defaultValue: 0,
            },
          ],
        },
        //关联申请单号
        associate: {
          modalTitle: "关联运管审批单号",
          formConfig: [
            {
              field: "applyNo",
              element: "el-input",
              title: "申请单号",
              attrs: {
                maxlength: 20,
                placeholder: "请输入运管审批单号",
              },
              rules: [{ required: true, message: "请输入申请单号！" }],
            },
          ],
        },
      };
      return form[this.operationType];
    },
  },
  created() {
    this.getTreeselect();
    this.getListUser();
  },
  mounted() {
    this.orderId = this.$route.query.orderId;
    this.orderNo = this.$route.query.orderNo;
    this.getBaseInfo();
    this.handleClick();
  },
  methods: {
    // 接单
    handleAcceptOrder(row) {
      this.$confirm("确定要接单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          orderNo: row.orderNo,
        };
        api.acceptOrder(params).then((res) => {
          this.$message.success("接单成功");
          this.loadData();
        });
      });
    },
    //关联申请单号
    handleAssociate(row) {
      this.operationType = "associate";
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig),
        orderId: row.orderId,
        orderNo: row.orderNo,
        applyNo: row.applyNo,
      });
    },
    //查看审批进度
    handleProcess(row) {
      this.$refs.checkProcessDrawer.open(row);
    },
    loadData() {
      this.getBaseInfo();
      this.handleClick();
    },
    //加单
    handleAddOrder(row) {
      this.$refs.addOrderDrawer.open(row);
    },
    //审核
    handleCheck(row) {
      this.$refs.checkOrderDrawer.open(row);
    },
    modalConfirmHandler(row) {
      // crudOperationType:transfer/withdraw/remark/end
      api[this.operationType](row).then((res) => {
        if (res?.code === "10000") {
          this.$message.success(`提交成功`);
          this.loadData();
        }
      });
    },
    //备注
    handleRemark(row) {
      this.operationType = "remark";
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig),
        orderTypeEnum: row.mainOrderNo ? "2" : "1",
        orderNo: row.orderNo,
      });
    },
    //完成
    handleEnd(row) {
      this.operationType = "end";
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig),
        orderNo: row.orderNo,
      });
    },
    //转派
    handleTransfer(row) {
      this.operationType = "transfer";
      this.getListUser();
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig),
        orderNo: row.orderNo,
      });
    },
    closeHandleDrawer() {
      this.loadData();
      this.handleVisible = false;
    },
    // 处理抽屉
    handleDrawer(row) {
      this.handleVisible = true;
      this.$refs.handleDrawer.getDetail();
    },
    //跳转到新增/编辑/加单
    JumpToEdit(row, type = "add") {
      this.$router.push({
        path: `/ledger/ledgerManage/${type}`,
        query: {
          type: type,
          orderId: row.orderId,
          orderNo: row.orderNo,
        },
      });
    },
    handleCommand(command, row) {
      this.moreBtnList?.find((x) => x.command == command)?.clickFn(row);
      console.log(command, row, "command");
    },
    getBaseInfo() {
      const addOrderId = this.$route.query.addOrderId;
      const method = addOrderId
        ? "queryAddOrderBaseInfo"
        : "queryLedgerBaseInfo";
      api[method]({
        orderId: this.orderId,
        orderNo: this.orderNo,
        addOrderId: addOrderId,
      }).then((res) => {
        if (res?.code === "10000") {
          this.orderId = res.data?.orderId;
          this.basicForm = res.data;
          this.orderStatus = res.data?.orderStatus;
        }
      });
    },
    handleClick() {
      this.$refs[this.activeName]?.getDetail({
        ...this.$route.query,
        ...this.basicForm,
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
      });
    },
    //后台返回的数据如果和VueTreeselect要求的数据结构不同，需要进行转换
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.$refs.formModal.setFormFields({ handleUser: undefined });
      this.getListUser({ orgNo: data.deptId });
    },
    beforeClearAll() {
      this.$refs.formModal.setFormFields({
        handleUser: undefined,
        deptId: undefined,
      });
      this.getListUser();
    },
    //获取用户列表
    async getListUser(param) {
      let params = { ...param, pageNum: 1, pageSize: 9999 };
      const res = await listAllUser(params);
      if (res.code != 10000) return;
      this.userOption = res.data?.map((x) => {
        return {
          ...x,
          value: x.userId,
          label: x.userName + "-" + x.nickName + "-" + (x.phonenumber || ""),
        };
      });
    },
  },
};
</script>

<style lang="less" scoped>
.box-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
