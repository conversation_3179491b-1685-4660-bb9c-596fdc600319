<template>
  <el-dialog
    title="批量导入工单"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    @close="handleBatchCancel"
  >
    <el-form :model="batchForm" ref="batchForm" label-width="140px">
      <!-- 模板类型选择 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="模板类型:" prop="templateType">
            <el-radio-group
              v-model="batchForm.templateType"
              @change="handleTemplateTypeChange"
            >
              <el-radio label="normal">常规模板</el-radio>
              <el-radio label="byOrderType">按工单类型下载模板</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 工单类型选择 -->
      <el-row v-if="batchForm.templateType === 'byOrderType'">
        <el-col :span="24">
          <el-form-item label="工单类型:" prop="orderType">
            <el-cascader
              v-model="batchForm.orderType"
              :options="orderTypeOptions"
              :props="{
                checkStrictly: false,
                multiple: true,
                value: 'id',
                label: 'typeName',
                children: 'childrenList',
              }"
              collapseTags
              filterable
              clearable
              placeholder="请选择工单类型"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 下载模板按钮 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="下载模版:">
            <el-button
              type="primary"
              @click="handleDirectDownload"
              :loading="downloadLoading"
              icon="el-icon-download"
            >
              下载模板
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="上传文件:" prop="file">
            <el-upload
              ref="upload"
              :limit="1"
              accept=".xlsx, .xls"
              :headers="upload.headers"
              :action="upload.url"
              :disabled="upload.isUploading"
              :on-success="handleFileSuccess"
              :on-error="handleFileError"
              :on-change="handleChangeFile"
              :auto-upload="false"
              :data="extraData"
            >
              <el-button>选择文件</el-button>
              <div slot="tip" class="el-upload__tip">
                上传格式支持xlxs、xls文件，500m以内。
              </div>
            </el-upload>
          </el-form-item></el-col
        >
      </el-row>
      <!-- 数据处理方式 - 仅常规模板显示 -->
      <el-row v-if="batchForm.templateType === 'normal'">
        <el-col :span="24">
          <el-form-item label="数据处理方式:" prop="orderStatus">
            <el-radio-group v-model="batchForm.orderStatus">
              <el-radio label="1">
                处理中（开启流程，仅第一个节点默认已处理）
              </el-radio>
              <el-radio label="2">
                完成（批量完成所有工单，将所有节点变成已处理）
              </el-radio>
              <el-radio label="0">
                草稿（不开启流程，导入后工单状态为草稿）
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 完成原因 -->
      <el-row
        v-if="
          batchForm.templateType === 'normal' && batchForm.orderStatus == '2'
        "
      >
        <el-col :span="24">
          <el-form-item label="完成原因:" prop="finishRemark">
            <el-input
              v-model="batchForm.finishRemark"
              rows="5"
              maxlength="500"
              show-word-limit
              type="textarea"
              placeholder="500个字符以内"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <slot name="extraForm" :params="extraData"></slot> -->
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        @click="handleBatchCancel"
        size="small"
        :loading="submitLoading"
        >取 消
      </el-button>
      <el-button
        type="primary"
        @click="handleBatchSubmit"
        size="small"
        :loading="submitLoading"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
import { fileDownLoad } from "@/utils/downLoad.js";
import { queryDeptOrderTree } from "@/api/ledger/workOrderType.js";
import api from "@/api/ledger/index.js";
export default {
  props: {
    //上传时附带的额外参数
    // extraData: {
    //   type: Object,
    //   default: () => {},
    // },
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      visible: false,
      submitLoading: false,
      batchForm: {
        file: [],
        templateType: "normal",
        orderType: [],
        orderStatus: "1",
        finishRemark: "",
      },
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + "/ledger/order/import",
        updateAsCode: "",
      },
      // 工单类型和业务类型选项
      orderTypeOptions: [],
      // 下载按钮加载状态
      downloadLoading: false,
      extraData: { orderStatus: "1", finishRemark: "" },
    };
  },
  watch: {
    batchForm: {
      handler(val) {
        this.extraData = {
          orderStatus: val.orderStatus,
        };
        if (this.batchForm.orderStatus == "2") {
          this.extraData.finishRemark = val.finishRemark;
        }
      },
      deep: true,
    },
  },

  created() {
    this.loadOrderTypeOptions();
  },
  methods: {
    // 加载工单类型选项
    loadOrderTypeOptions() {
      queryDeptOrderTree({}).then((res) => {
        this.orderTypeOptions = res.data?.map((x) => ({ ...x }));
      });
    },
    // 处理模板类型变化
    handleTemplateTypeChange(value) {
      // 清空相关字段
      if (value === "normal") {
        this.batchForm.orderType = [];
        this.batchForm.orderStatus = "1";
        this.batchForm.finishRemark = "";
      } else {
        this.batchForm.orderType = [];
        this.batchForm.orderStatus = "";
        this.batchForm.finishRemark = "";
      }
    },

    // 直接下载模板
    async handleDirectDownload() {
      if (
        this.batchForm.templateType == "byOrderType" &&
        this.batchForm.orderType?.length === 0
      ) {
        this.$message.error("请选择工单类型！");
        return;
      }
      this.downloadLoading = true;
      try {
        await this.handleTemplateDownload(this.batchForm);
      } finally {
        this.downloadLoading = false;
      }
    },
    // 处理模板下载
    async handleTemplateDownload(formData) {
      console.log("handleTemplateDownload", formData);
      try {
        if (formData.templateType === "normal") {
          // 下载常规模板
          this.downloadNormalTemplate();
          // this.$message.success("模板下载成功");
        } else {
          await this.downloadTemplateByOrderType(formData.orderType);
          // this.$message.success("模板下载成功");
        }
      } catch (error) {
        console.error("下载模板失败:", error);
        this.$message.error("下载模板失败，请稍后重试");
      }
    },
    // 下载常规模板
    downloadNormalTemplate() {
      window.location.href =
        "/charging-maintenance-ui/static/工单台账导入模板.xlsx";
    },
    // 按工单类型下载模板
    async downloadTemplateByOrderType(orderType) {
      try {
        const res = await api.downloadCustomTemplate({
          orderTypeList: orderType,
        });
        if (res) {
          await fileDownLoad(res);
          // this.$refs.templateModal.closeVisible();
        }
      } catch (error) {
        console.error("下载失败:", error);
        this.$message.error("下载失败，请稍后重试");
        return false;
      }
    },
    open() {
      this.visible = true;
    },
    handleFileSuccess(response) {
      this.submitLoading = false;
      console.log("response===", response);
      if (!response.success) {
        this.$confirm(response.message, "导入失败！", {
          confirmButtonText: "重新上传",
          cancelButtonText: "取消",
          type: "error",
          center: true,
          dangerouslyUseHTMLString: true,
        })
          .then(() => {
            this.batchForm.file = [];
            this.$refs.upload.clearFiles();
          })
          .catch(() => {
            this.handleBatchCancel();
          });
      } else {
        this.handleBatchCancel();
        this.$alert("导入成功", "导入结果", {
          type: "success",
          confirmButtonText: "我知道了",
          callback: () => {
            this.$emit("uploadSuccess");
          },
        });
      }
    },
    handleFileError(response) {
      this.submitLoading = false;
      this.$confirm(response.message, "导入失败！", {
        confirmButtonText: "重新上传",
        cancelButtonText: "取消",
        type: "error",
        center: true,
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          this.batchForm.file = [];
          this.$refs.upload.clearFiles();
        })
        .catch(() => {
          this.handleBatchCancel();
        });
    },
    handleChangeFile(file, fileList) {
      console.log(file, fileList);
      this.batchForm.file = fileList || [];
    },
    //批量配置-提交
    handleBatchSubmit() {
      console.log("batchForm", this.batchForm);
      if (this.batchForm.file?.length === 0) {
        this.$message.error("请上传文件！");
        return;
      }
      if (this.batchForm.file[0].size / 1024 / 1024 > 500) {
        this.$message.error("上传的文件大小不能超过500m!");
        return;
      }

      // 根据模板类型选择不同的处理逻辑
      if (this.batchForm.templateType === "byOrderType") {
        // 工单类型模板场景：调用新的API接口

        this.handleDynamicImportSubmit();
      } else {
        // 常规模板场景：调用原有的API接口
        if (!this.batchForm.orderStatus) {
          this.$message.error("请选择数据处理方式！");
          return;
        }
        if (this.batchForm.orderStatus == "2" && !this.batchForm.finishRemark) {
          this.$message.error("完成原因不允许为空！");
          return;
        }
        console.log("extraData:", this.extraData);
        // this.extraData = {
        //   orderStatus: this.batchForm.orderStatus,
        //   finishRemark: this.batchForm.finishRemark,
        // };
        // 更新 extraData
        // this.$emit("updateExtraData", {
        //   orderStatus: this.batchForm.orderStatus,
        //   finishRemark: this.batchForm.finishRemark,
        // });
        this.submitLoading = true;
        this.$refs.upload.submit();
      }
    },

    // 处理动态导入提交
    async handleDynamicImportSubmit() {
      try {
        this.submitLoading = true;

        const formData = new FormData();
        formData.append("file", this.batchForm.file[0].raw);
        formData.append(
          "orderTypeList",
          JSON.stringify(this.batchForm.orderType)
        );

        const response = await api.dynamicImport(formData);

        if (response.success) {
          this.handleBatchCancel();
          this.$alert("导入成功", "导入结果", {
            type: "success",
            confirmButtonText: "我知道了",
            callback: () => {
              this.$emit("uploadSuccess");
            },
          });
        } else {
          this.$confirm(response.message, "导入失败！", {
            confirmButtonText: "重新上传",
            cancelButtonText: "取消",
            type: "error",
            center: true,
            dangerouslyUseHTMLString: true,
          })
            .then(() => {
              this.batchForm.file = [];
              this.$refs.upload.clearFiles();
            })
            .catch(() => {
              this.handleBatchCancel();
            });
        }
      } catch (error) {
        console.error("动态导入失败:", error);
        this.$message.error("导入失败，请稍后重试");
      } finally {
        this.submitLoading = false;
      }
    },

    handleBatchCancel() {
      this.visible = false;
      this.$refs.batchForm.resetFields();
      // 重置表单数据
      this.batchForm = {
        file: [],
        templateType: "normal",
        orderType: [],
        orderStatus: "1",
        finishRemark: "",
      };
      this.$refs.upload.clearFiles();
    },
  },
};
</script>

<style></style>
