//基本信息
<template>
  <div>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <DynamicForm
        ref="baseForm"
        :config="baseConfig"
        :params="baseParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
        :preview="true"
      >
        <template #docList>
          <div v-for="(item, index) in baseParams.annexList" :key="index">
            <el-link @click="handlePreview(index)">{{ item.docName }}</el-link>
          </div>
          <PreviewFiles
            :initial-index="previewIndex"
            v-if="showViewer"
            :on-close="
              () => {
                showViewer = false;
              }
            "
            :url-list="baseParams.annexList"
            :fileOptions="{ url: 'storePath', name: 'docName' }"
          />
        </template>
        <template #stations>
          <div v-for="(item, index) in baseParams.stations" :key="index">
            <el-form-item label="站点名称：">{{
              item.stationName
            }}</el-form-item>
            <el-form-item label="踏勘编码：" v-if="item.businessId">{{
              item.businessId
            }}</el-form-item>
            <el-form-item label="省市区：" v-if="!item.businessId">{{
              item.regionStr
            }}</el-form-item>
            <el-form-item label="详细地址：" v-if="!item.businessId">{{
              item.detailAddress
            }}</el-form-item>
          </div>
        </template>
      </DynamicForm>
    </el-card>
    <el-card v-if="!onlyShowBase">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>其他信息</span>
      </div>
      <div v-for="(item, index) in formArr" :key="index">
        <FormCreatePreview :formData="item.formJson"></FormCreatePreview>
      </div>

      <!-- <div v-for="(item, index) in formArr" :key="index">
        <form-create
          :rule="item.formJson"
          v-model="dealFormData[index]"
          :option="item.formConfig"
        />
        <div v-if="item.formJson.showfileName">
          <div style="font-size:14px;margin:10px 0;">
            已上传的文件名称：
          </div>
          <div v-for="(j, i) in item.formJson" :key="i">
            <div v-if="j.fileList && j.fileList.length > 0">
              <div v-for="(f, fi) in j.fileList" :key="fi">
                {{ f.name }}
                <el-button type="text" size="small" @click="handleDownload(f)"
                  >下载</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div> -->
    </el-card>
    <el-card id="info" v-if="!onlyShowBase">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>工单来源</span>
      </div>
      <DynamicForm
        ref="originForm"
        :config="originConfig"
        :params="originParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="150px"
        :preview="true"
      ></DynamicForm>
    </el-card>
    <el-card id="info" v-if="handleUserParams.designateName">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>创建工单时所选的处理人</span>
      </div>
      <DynamicForm
        ref="originForm"
        :config="handleUserConfig"
        :params="handleUserParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="150px"
        :preview="true"
      ></DynamicForm>
    </el-card>
    <el-card
      id="info"
      v-if="
        hourParams.nonStandardMin ||
          hourParams.nonStandardMin === 0 ||
          hourParams.nonStandardMin === '0'
      "
    >
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>非标工时</span>
      </div>
      <DynamicForm
        ref="originForm"
        :config="hourConfig"
        :params="hourParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="150px"
        :preview="true"
      ></DynamicForm>
    </el-card>
  </div>
</template>

<script>
import api from "@/api/ledger/index.js";
import { initParams } from "@/utils/buse.js";
import { getToken } from "@/utils/auth";
import PreviewFiles from "@/components/PreviewFiles/index.vue";
import formCreateMixin from "@/mixin/formCreate.js";
import FormCreatePreview from "@/components/formCreatePreview/index.vue";
export default {
  components: { PreviewFiles, FormCreatePreview },
  mixins: [formCreateMixin],
  props: {
    onlyShowBase: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      addOrderId: "",
      originParams: {},
      showfileName: false,
      formArr: [],
      dealFormData: [],
      baseParams: {},
      showViewer: false,
      previewIndex: 0,
      urgencyLevelOptions: [],
      demandSourceOptions: [],
      dynamicParams: {},
      createWayOptions: [],
      handleUserParams: {},
      hourParams: {},
    };
  },
  computed: {
    hourConfig() {
      return [
        {
          field: "nonStandardMin",
          title: "工时：",
          previewFormatter: (val) => {
            return (val ?? "-") + "分钟";
          },
        },
      ];
    },
    handleUserConfig() {
      return [
        {
          field: "designateName",
          title: "处理人：",
        },
      ];
    },
    originConfig() {
      return [
        {
          field: "createWay",
          title: "工单创建方式：",
          previewFormatter: (val) => {
            return this.createWayOptions?.find((x) => x.dictValue == val)
              ?.dictLabel;
          },
        },
        // {
        //   field: "mainOrderNo",
        //   title: "关联的主工单号：",
        //   previewFormatter: (val) => {
        //     return (
        //       <div>
        //         <el-link on={{ click: () => this.jumpToPage(val) }}>
        //           {val}
        //         </el-link>
        //         <i
        //           class="el-icon-document-copy pointer-icon"
        //           on={{ click: () => this.copyToClipboard(val) }}
        //         ></i>
        //       </div>
        //     );
        //   },
        //   show: this.originParams.mainOrderNo !== undefined,
        // },
        // {
        //   field: "mainOrderStatus",
        //   title: "主工单状态：",
        //   show: this.originParams.mainOrderNo !== undefined,
        // },
      ];
    },
    baseConfig() {
      return [
        {
          field: "orderNo",
          title: "工单编码：",
          previewFormatter: (val) => {
            return (
              <div>
                <span>{val}</span>
                <i
                  class="el-icon-document-copy pointer-icon"
                  on={{ click: () => this.copyToClipboard(val) }}
                ></i>
              </div>
            );
          },
          show: !this.onlyShowBase,
        },
        {
          field: "orderStatus",
          title: "工单状态：",
          show: !this.onlyShowBase,
        },
        {
          field: "businessTypeStr",
          title: "业务类型：",
        },
        {
          field: "orderTypeStr",
          title: "工单类型：",
        },
        {
          field: "urgencyLevelName",
          title: "紧急程度：",
          // previewFormatter: (val) => {
          //   return this.urgencyLevelOptions?.find((x) => x.dictValue == val)
          //     ?.dictLabel;
          // },
        },
        {
          field: "introduction",
          title: "问题简介：",
        },
        {
          field: "orderDesc",
          title: "问题描述：",
        },
        {
          field: "demandSource",
          title: "需求来源：",
          previewFormatter: (val) => {
            return this.demandSourceOptions?.find((x) => x.dictValue == val)
              ?.dictLabel;
          },
        },
        {
          field: "annexList",
          title: "附件/图片：",
          previewSlot: "docList",
          defaultValue: [],
        },
        // {
        //   field: "stationName",
        //   title: "站点名称：",
        // },
        // {
        //   field: "regionStr",
        //   title: "省市区：",
        // },
        // {
        //   field: "detailAddress",
        //   title: "详细地址：",
        // },
        {
          field: "stations",
          title: "",
          previewSlot: "stations",
          defaultValue: [],
          itemProps: {
            labelWidth: "0px",
          },
          formItemStyle: {
            marginBottom: 0,
          },
        },
        {
          field: "companyName",
          title: "公司名称：",
        },
        {
          field: "companyCategory",
          title: "公司类别：",
          show: !this.onlyShowBase,
        },
        {
          field: "companyAttribute",
          title: "公司属性：",
          show: !this.onlyShowBase,
        },
        {
          field: "createUserName",
          title: "创建人：",
          show: !this.onlyShowBase,
        },
        {
          field: "createTime",
          title: "创建时间：",
          show: !this.onlyShowBase,
        },
        {
          field: "finishTime",
          title: "完成时间：",
          show: !this.onlyShowBase,
        },
        {
          field: "finishWay",
          title: "完成方式：",
          previewFormatter: (val) => {
            return [
              { value: 1, label: "正常结束" },
              { value: 2, label: "客服工单关闭" },
              { value: 3, label: "主工单关闭" },
              { value: 4, label: "作废" },
              { value: 5, label: "手动结束" },
            ]?.find((x) => x.value == val)?.label;
          },
          show: !this.onlyShowBase,
        },
        {
          field: "remark",
          title: "备注：",
          show: !this.onlyShowBase,
        },
      ];
    },
  },
  created() {
    this.baseParams = initParams(this.baseConfig);
    this.originParams = initParams(this.originConfig);
    this.handleUserParams = initParams(this.handleUserConfig);
    this.hourParams = initParams(this.hourConfig);
    this.getDicts("urgency_level").then((response) => {
      this.urgencyLevelOptions = response?.data;
    });
    this.getDicts("ledger_demand_source").then((response) => {
      this.demandSourceOptions = response?.data;
    });
    this.getDicts("ledger_create_way").then((response) => {
      this.createWayOptions = response?.data;
    });
  },
  methods: {
    jumpToPage(val) {
      this.$router.push({
        name: "ledgerList",
        params: {
          orderNo: val,
        },
      });
    },
    copyToClipboard(val) {
      // 创建一个textarea元素
      const textarea = document.createElement("textarea");
      // 设置textarea的值为要复制的文本
      textarea.value = val;
      // 将textarea添加到文档中
      document.body.appendChild(textarea);
      // 选中textarea中的文本
      textarea.select();
      try {
        // 尝试复制选中的文本
        const successful = document.execCommand("copy");
        const msg = successful ? "复制成功" : "复制失败";
        console.log(msg);
        this.$message.success(msg);
      } catch (err) {
        console.log("不能使用这种方法复制", err);
      }
      // 将textarea从文档中移除
      document.body.removeChild(textarea);
    },
    //附件预览
    handlePreview(index) {
      this.showViewer = true;
      this.previewIndex = index;
    },
    getDetail(row) {
      this.addOrderId = row.addOrderId;
      console.log(this.addOrderId, "add");
      const method = row.addOrderId
        ? "queryAddOrderBaseInfo"
        : "queryLedgerBaseInfo";
      api[method]({
        orderId: row.orderId,
        addOrderId: row.addOrderId,
        orderNo: row.orderNo,
      }).then((res) => {
        if (res?.code === "10000") {
          this.baseParams = { ...this.baseParams, ...res.data };
          this.handleUserParams = {
            designateName: res.data.designateName || "",
          };
          this.hourParams = { nonStandardMin: res.data.nonStandardMin || "" };
          this.originParams = {
            ...this.originParams,
            ...res.data,
          };
          this.formArr = res.data?.preDealForms?.map((x) => {
            return {
              formConfig: JSON.parse(x.formConfig),
              formJson: JSON.parse(x.formJson)?.map((item) => {
                this.handlePreviewFormRule(item);
                return item;
              }),
              dealFormData: {},
            };
          });
          const n = [
            { type: "input", element: "el-input" },
            { type: "inputNumber", element: "el-input-number" },
            { type: "radio", element: "el-radio-group" },
            { type: "checkbox", element: "el-checkbox-group" },
            { type: "select", element: "el-select" },
          ];

          // this.formConfig = JSON.parse(res.data.formConfig);
          // this.formJson = JSON.parse(res.data.formJson)?.map((item) => {
          //   this.handlePreviewFormRule(item);
          //   return item;
          // });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.pointer-icon {
  cursor: pointer;
  margin-left: 5px;
  color: #bbbbbb;
  &:hover {
    color: #029c7c;
  }
}
</style>
