<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['maintenance:report:export'])"
      @handleExport="handleExport"
    >
      <template slot="orderTypeName">
        <el-row>
          <el-col :span="8">
            <el-select
              v-model="searchForm.orderTypeParentName"
              clearable
              filterable
              style="width: 100%"
              @change="
                (val) => {
                  return orderTypeParentChange(val, 2);
                }
              "
            >
              <el-option
                v-for="item in orderTypeParentOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.orderTypeName"
              clearable
              filterable
              style="width: 100%"
              :disabled="searchForm.tag == '1' || searchForm.tag == '2'"
              @change="
                (val) => {
                  return orderTypeParentChange(val, 3);
                }
              "
            >
              <el-option
                v-for="item in orderTypeOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.threeOrderTypeName"
              clearable
              filterable
              style="width: 100%"
              :disabled="searchForm.tag == '1' || searchForm.tag == '2'"
            >
              <el-option
                v-for="item in threeOrderTypeOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
        </el-row>
      </template>
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="'orderId'"
        :batchDelete="true"
        row-id="orderId"
      >
        <template slot="stationTag" slot-scope="{ row }">
          <el-tag
            style="margin-right: 10px;margin-bottom: 5px"
            :key="index"
            v-for="(item, index) in row.stationTagList"
            >{{ item }}</el-tag
          >
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['maintenance:report:detail']"
          >
            查看
          </el-button>
        </template>
        <template slot="jump" slot-scope="{ row, column }">
          <el-button
            type="text"
            size="large"
            @click="handleJump(row, column.property)"
          >
            {{ row[column.property] }}
          </el-button>
        </template>
        <template slot="projectJump" slot-scope="{ row, column }">
          <div
            v-for="(item, index) in getArr(row[column.property])"
            :key="index"
          >
            <el-tooltip :content="item" placement="top">
              <el-button
                type="text"
                size="large"
                @click="handleJump(item, column.property)"
              >
                <div
                  style="max-width: 200px;overflow: hidden; white-space: nowrap; text-overflow: ellipsis;"
                >
                  {{ item }}
                </div>
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";

import { getAllDeptList } from "@/api/operationWorkOrder";
import { getToken } from "@/utils/auth";
import { regionData } from "element-china-area-data";
import { queryTreeList } from "@/api/workOrderType/index.js";

import {
  childrenList,
  fistLevelList,
  queryOrderReportlist,
  exportReport,
  getReportPDF,
} from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";

export default {
  name: "report",
  components: {
    AdvancedForm,
    GridTable,
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      orderTypeOptions: [],
      businessTypeOptions: [],
      threeOrderTypeOptions: [],
      orderTypeParentOptions: [
        { dictLabel: "故障工单", dictValue: "故障工单" },
        { dictLabel: "抄表工单", dictValue: "抄表工单" },
        { dictLabel: "真车充电测试派单", dictValue: "真车充电测试派单" },
      ],
      channelOptions: [
        { dictLabel: "手动", dictValue: "01" },
        { dictLabel: "客服", dictValue: "02" },
        { dictLabel: "充电平台", dictValue: "03" },
      ],
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + "/excel/stationGradeImport",
        updateAsCode: "",
      },
      rules: {
        gradeId: [
          { required: true, message: "请选择站点运维等级", trigger: "change" },
        ],
      },
      stationId: undefined,
      statusDict: [],
      stationChargeTypeDict: [],
      columns: [
        {
          field: "orderNo",
          title: "工单编号",
        },
        {
          field: "channel",
          title: "工单来源",
          showOverflowTooltip: true,
          formatter: this.channelFormat,
        },
        {
          field: "businessType",
          title: "业务类型",
          showOverflowTooltip: true,
          formatter: this.businessTypeFormat,
        },
        {
          field: "orderTypeName",
          title: "工单类型",
          showOverflowTooltip: true,
          formatter: this.orderTypeFormat,
        },
        {
          field: "deviceName",
          title: "设备名称",
          showOverflowTooltip: true,
          customWidth: 180,
        },
        {
          field: "deviceNo",
          title: "设备编码",
          showOverflowTooltip: true,
          customWidth: 180,
        },
        {
          field: "stationName",
          title: "站点名称",
          showOverflowTooltip: true,
          slots: { default: "jump" },
        },

        {
          field: "deptName",
          showOverflowTooltip: true,
          title: "能投大区",
        },
        {
          field: "provinceAddress",
          showOverflowTooltip: true,
          title: "地址",
          customWidth: 150,
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "handleTime",
          title: "处理时间",
        },
        {
          field: "handleUserName",
          title: "处理人",
        },
        {
          field: "auditTime",
          title: "审核时间",
        },
        {
          field: "auditUserNickName",
          title: "审核人",
        },
        {
          title: "操作",
          minWidth: 100,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        orderTypeName: "",
        orderTypeParentName: "",
        threeOrderTypeName: "",
        oneFaultType: "",
        threeFaultType: "",
        twoFaultType: "",
      },
      total: 0,
      loading: false,
      rowInfo: {},
      stationTypeDict: [],
      stationTagDict: [],
      pageType: "detail",
      projectId: undefined,
      dialogTitle: "项目详情",
      tagManageVisible: false,
      handRow: {
        stationIds: [],
      },
      token: "",
      recordList: [],
      logVisible: false,
      levelVisible: false,
      levelOptions: [],
      deptOptionList: [],
      operationModeOptions: [],
      submitLoading: false,

      flattenData: [],
    };
  },
  async created() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    this.getOrderTypeOptions();
    this.getBusinessTypeOptions();
    this.getTreeData();
    Promise.all([
      this.getDicts("cm_station_type").then((response) => {
        this.stationTypeDict = response?.data;
      }),
      this.getDicts("cm_station_tag").then((response) => {
        this.stationTagDict = response?.data;
      }),
      this.getDicts("cm_operation_mode").then((response) => {
        this.operationModeOptions = response?.data;
      }),
      this.getDicts("cm_station_status").then((response) => {
        this.statusDict = response?.data;
      }),
      this.getDicts("cm_station_charge_type").then((response) => {
        this.stationChargeTypeDict = response?.data;
      }),
      this.getDeptList(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          // this.initConfig();
          this.getList();
        });
      }, 500);
    });
  },
  mounted() {
    this.token = getToken();
  },
  methods: {
    getTreeData() {
      queryTreeList({}).then((res) => {
        this.flattenData = this.flattenArray(res.data);
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          delete item.childrenList; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    channelFormat({ cellValue }) {
      return this.selectDictLabel(this.channelOptions, cellValue);
    },
    //工单类型转换
    orderTypeFormat({ cellValue, row }) {
      const arr = [
        row.orderTypeParentName,
        row.orderTypeName,
        row.threeOrderTypeName,
      ];
      return arr.filter((x) => x).join(" | ");
    },
    //业务类型转换
    businessTypeFormat({ cellValue, row }) {
      return this.selectDictLabel(this.businessTypeOptions, cellValue) == ""
        ? row.businessTypeName
        : this.selectDictLabel(this.businessTypeOptions, cellValue);
    },
    getBusinessTypeOptions() {
      this.getDicts("order_business_type").then((response) => {
        this.businessTypeOptions = response?.data;
      });
    },
    getOrderTypeOptions() {
      fistLevelList().then((res) => {
        this.orderTypeParentOptions = res?.data?.map((x) => {
          return { label: x.typeName, value: x.id };
        });
        this.orderTypeParentOptions.push(
          ...[
            { label: "全部", value: "all" },
            { label: "其他（已删除类型）", value: "other" },
          ]
        );
      });
    },
    getArr(item) {
      console.log(item == "" ? [] : item?.split(";"));
      return item == "" ? [] : item?.split(";");
    },
    checkPermission,
    handleBeforeUpload(file) {
      const isLt2G = file.size / 1024 / 1024 / 1024 < 2;
      if (!isLt2G) {
        this.$message.error("上传的文件大小不能超过2G!");
      }
      return isLt2G;
    },

    //能投大区下拉选项
    async getDeptList() {
      getAllDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    handleJump(row, property) {
      const arr = [
        {
          title: "站点名称",
          field: "stationName",
          method: () => {
            this.$router.push({
              path: "/operationMaintenanceManage/station/newDetailPage",
              query: {
                stationId: row.stationId,
                projectId: row.projectId,
                stationCode: row.stationCode,
                stationName: row.stationName,
              },
            });
          },
        },
        {
          field: "gunACCount",
          title: "交流枪数",
          method: () => {
            this.$router.push({
              name: "maintenanceChargingGun",
              params: {
                stationNo: row.stationCode,
                subTypeCode: "01",
              },
            });
          },
        },
        {
          field: "gunDCCount",
          title: "直流枪数",
          method: () => {
            this.$router.push({
              name: "maintenanceChargingGun",
              params: {
                stationNo: row.stationCode,
                subTypeCode: "02",
              },
            });
          },
        },
        {
          field: "projectCode",
          title: "项目编码",
          method: () => {
            this.$router.push({
              name: "projectManage",
              params: {
                projectCode: row,
              },
            });
          },
        },
        {
          field: "projectName",
          title: "项目名称",
          method: () => {
            this.$router.push({
              name: "projectManage",
              params: {
                projectName: row,
              },
            });
          },
        },
      ];
      arr.find((item) => {
        if (item.field == property) {
          item.method();
        }
      });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
    },
    handleRowLevel(row) {
      this.levelVisible = true;
      this.stationId = row.stationId;
    },
    closeDialog() {
      this.logVisible = false;
    },
    handleExport() {
      const params = this.getQueryParams();
      this.$confirm("是否确认导出所有数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportReport(params).then(() => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },

    showDetail(row) {
      getReportPDF(row.orderNo).then((res) => {
        if (res.code === "10000") {
          window.open(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getQueryParams() {
      const {
        orderNo,
        handleUserName,
        orderTypeParentName,
        orderTypeName,
        threeOrderTypeName,
        channel,
        auditUserNickName,
        stationName,
        orgNo,
        deviceName,
        deviceNo,
        region = [],
        createTime,
        handleTime,
        auditTime,
        pageNum,
        pageSize,
      } = this.searchForm;

      const provinceList = [];
      const cityList = [];
      const countyList = [];
      region.forEach((i) => {
        i[0] && provinceList.push(i[0]);
        i[1] && cityList.push(i[1]);
        i[2] && countyList.push(i[2]);
      });

      const startCreateTime = createTime?.[0]
        ? createTime[0] + " 00:00:00"
        : null;
      const endCreateTime = createTime?.[1]
        ? createTime?.[1] + " 23:59:59"
        : null;
      const startHandleTime = handleTime?.[0]
        ? handleTime?.[0] + " 00:00:00"
        : null;
      const endHandleTime = handleTime?.[1]
        ? handleTime?.[1] + " 23:59:59"
        : null;
      const startAuditTime = auditTime?.[0]
        ? auditTime?.[0] + " 00:00:00"
        : null;
      const endAuditTime = auditTime?.[1] ? auditTime?.[1] + " 23:59:59" : null;

      const params = {
        orderNo,
        handleUserName,
        orderTypeParentName,
        orderTypeName,
        threeOrderTypeName,
        channel,
        auditUserNickName,
        stationName,
        orgNo,
        deviceName,
        deviceNo,
        provinceList: [...new Set(provinceList)],
        cityList: [...new Set(cityList)],
        countyList: [...new Set(countyList)],
        startCreateTime,
        endCreateTime,
        startHandleTime,
        endHandleTime,
        startAuditTime,
        endAuditTime,
        pageNum,
        pageSize,
      };
      params["orderTypeParentName"] = this.flattenData?.find(
        (x) => x.id == orderTypeParentName
      )?.typeName;
      params["orderTypeName"] = this.flattenData?.find(
        (x) => x.id == orderTypeName
      )?.typeName;
      params["threeOrderTypeName"] = this.flattenData?.find(
        (x) => x.id == threeOrderTypeName
      )?.typeName;

      return params;
    },
    getList() {
      this.loading = true;
      let params = this.getQueryParams();
      queryOrderReportlist(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      // this.
    },
    //站点类型转换
    stationTypeFormat({ cellValue }) {
      return this.selectDictLabel(this.stationTypeDict, cellValue);
    },
    close() {
      this.tagManageVisible = false;
    },
    orderTypeParentChange(val, level) {
      if (val === "all") {
        this.searchForm.tag = 1;
        this.searchForm.orderTypeName = undefined;
        this.searchForm.threeOrderTypeName = undefined;
        return;
      }
      if (val === "other") {
        this.searchForm.tag = 2;
        this.searchForm.orderTypeName = undefined;
        this.searchForm.threeOrderTypeName = undefined;
        return;
      }
      this.searchForm.tag = 0;
      if (level == 2) {
        this.searchForm.orderTypeName = undefined;
        this.searchForm.threeOrderTypeName = undefined;
        this.threeOrderTypeOptions = [];
        childrenList({ id: val }).then((res) => {
          this.orderTypeOptions = res?.data?.map((x) => {
            return { label: x.typeName, value: x.id };
          });
        });
      } else {
        this.searchForm.threeOrderTypeName = undefined;
        childrenList({ id: val }).then((res) => {
          this.threeOrderTypeOptions = res?.data?.map((x) => {
            return { label: x.typeName, value: x.id };
          });
        });
      }
    },
  },
  computed: {
    config() {
      return [
        {
          key: "orderNo",
          title: "工单编号",
          type: "input",
          placeholder: "请填写工单编号",
        },
        {
          key: "handleUserName",
          title: "处理人",
          type: "input",
          placeholder: "请填写处理人",
        },
        {
          key: "orderTypeName",
          parentKey: "orderTypeParentName",
          title: "工单类型",
          type: "slot",
          placeholder: "请选择工单类型",
        },
        {
          key: "channel",
          title: "工单来源",
          type: "select",
          placeholder: "请选择工单来源",
          options: this.channelOptions,
        },
        {
          key: "auditUserNickName",
          title: "审核人",
          type: "input",
          placeholder: "请填写审核人",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
          showOverflowTooltip: true,
        },

        {
          key: "orgNo",
          title: "能投大区",
          type: "select",
          placeholder: "请选择能投大区",
          options: this.deptOptionList,
        },
        {
          key: "createTime",
          title: "创建时间",
          type: "dateRange",
          placeholder: "请选择创建时间",
        },

        {
          key: "deviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请填写设备名称",
        },
        {
          key: "deviceNo",
          title: "设备编码",
          type: "input",
          placeholder: "请填写设备编码",
        },
        {
          key: "handleTime",
          title: "处理时间",
          type: "dateRange",
          placeholder: "请选择处理时间",
        },
        {
          key: "auditTime",
          title: "审核时间",
          type: "dateRange",
          placeholder: "请选择审核时间",
        },
      ];
    },
  },
};
</script>

<style scoped lang="less"></style>
