/**
 * LuckySheet 表格配置文件
 * 包含默认表头和列宽配置
 */

// 默认表头配置
// 以 * 开头的字段为必填字段，会在表格中显示为红色
export const defaultHeaders = [
  "*运营商名称",
  "*所属省市",
  "*场站名称",
  "场站编码",
  "*活动生效日期",
  "*活动结束日期",
  "*结算比例（%）",
  "分润配置（分润收入）（%）",
  "*长协（通用分润）（%）",
  "备注",
  "正确站点名称",
  "差异类型",
  "开始时间",
  "结束时间",
  "应收分润（%）",
  "实际执行分润（%）",
  "原因",
  "差异额（%）",
];

// 默认列宽配置
export const defaultColumnConfig = {
  0: 150, // 运营商名称
  1: 150, // 场站名称
  2: 150, // 场站编码
  3: 200, // 所属省市
  4: 120, // 活动生效日期
  5: 120, // 活动结束日期
  6: 120, // 结算比例
  7: 220, // 长协（通用分润）
  8: 200, // 差异类型
  9: 150, // 原因
  10: 120, // 开始时间
  11: 120, // 结束时间
  12: 150, // 分润配置
  13: 120, // 应收分润
  14: 120, // 实际执行分润
  15: 180, // 差异额
  16: 220,
  17: 120,
  18: 120,
};

// 创建默认空表格数据
export const createEmptySheetData = (headers, columnConfig) => {
  // 创建表头行，设置必填字段为红色
  const headerRow = headers.map((title) => {
    const isRequired = title.startsWith("*");
    const displayTitle = isRequired ? title.substring(1) : title;

    return {
      v: displayTitle,
      ct: {
        fa: "General",
        t: "g",
      },
      // 设置表头样式：必填字段为红色，所有表头加粗居中
      bg: null, // 背景色
      bl: 1, // 下边框
      bt: 1, // 上边框
      br: 1, // 右边框
      bb: 1, // 底边框
      fc: isRequired ? "#ff0000" : "#000000", // 字体颜色
      ff: "Arial", // 字体
      fs: 11, // 字体大小
      it: 0, // 斜体
      ht: 2, // 水平对齐方式：2=居中
      vt: 1, // 垂直对齐方式：1=居中
      tb: 1, // 粗体
    };
  });

  // 创建一个函数来生成深拷贝的空行
  const createEmptyRow = () => {
    // 创建新的空行数组（完全独立）
    return headers.map((_, index) => {
      // 百分比列 - 深拷贝对象
      if ([6, 7, 8, 14, 15, 17].includes(index)) {
        return {
          v: "",
          ct: {
            ...{
              fa: '0.000"%"',
              t: "n",
            },
          }, // 复制ct对象
        };
      } else if ([4, 5, 12, 13].includes(index)) {
        return {
          v: "",
          ct: { fa: "yyyy-MM-dd hh:mm:ss", t: "d" },
        };
      }
      // 其他列
      return {
        v: "",
        ct: {
          ...{
            fa: "General",
            t: "g",
          },
        },
      };
    });
  };

  // 组合所有行
  const allRows = [
    headerRow,
    ...Array(10)
      .fill()
      .map(createEmptyRow),
  ];

  // 返回一个带有默认表头的空表格结构
  return {
    name: "场站配置",
    color: "",
    status: 1,
    order: 0,
    data: allRows,
    config: {
      columnlen: columnConfig,
      rowlen: {
        0: 40, // 表头行高
      },
    },
    // 默认冻结首行（表头行）
    frozen: {
      type: "row",
      index: 0,
    },
    index: 0,
  };
};
