<template>
  <div style="padding-bottom: 10px">
    <!-- 图片上传组件辅助 -->
    <el-upload
      class="avatar-uploader quill-img"
      :action="uploadImgUrl"
      name="file"
      :headers="headers"
      :show-file-list="false"
      :on-success="quillImgSuccess"
      :on-error="uploadError"
      :before-upload="quillImgBefore"
      accept=".jpg,.jpeg,.png,.gif"
      v-show="false"
    ></el-upload>
    <div class="editor"></div>

    <!-- 源代码编辑弹窗 -->
    <el-dialog
      title="HTML源代码编辑"
      :visible.sync="sourceCodeDialogVisible"
      width="80%"
      :before-close="handleSourceCodeDialogClose"
      append-to-body
    >
      <div class="source-code-editor">
        <el-input
          type="textarea"
          v-model="sourceCodeContent"
          :rows="20"
          placeholder="请输入或粘贴HTML代码..."
          class="source-code-textarea"
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelSourceCodeEdit">取 消</el-button>
        <el-button type="primary" @click="confirmSourceCodeEdit"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Quill from "quill";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
const titleConfig = {
  "ql-bold": "加粗",
  "ql-color": "颜色",
  "ql-font": "字体",
  "ql-code": "插入代码",
  "ql-italic": "斜体",
  "ql-link": "添加链接",
  "ql-background": "颜色",
  "ql-size": "字体大小",
  "ql-strike": "删除线",
  "ql-script": "上标/下标",
  "ql-underline": "下划线",
  "ql-blockquote": "引用",
  "ql-header": "标题",
  "ql-indent": "缩进",
  "ql-list": "列表",
  "ql-align": "文本对齐",
  "ql-direction": "文本方向",
  "ql-code-block": "代码块",
  "ql-formula": "公式",
  "ql-image": "图片",
  "ql-video": "视频",
  "ql-clean": "清除字体样式",
  "ql-upload": "文件",
  "ql-table": "插入表格",
  "ql-table-insert-row": "插入行",
  "ql-table-insert-column": "插入列",
  "ql-table-delete-row": "删除行",
  "ql-table-delete-column": "删除列",
  "ql-source-code": "源代码编辑",
};
// 字体白名单
const Font = Quill.import("formats/font");
Font.whitelist = [
  "SimSun",
  "SimHei",
  "Microsoft-YaHei",
  "KaiTi",
  "FangSong",
  "Arial",
  "Times-New-Roman",
  "sans-serif",
];
Quill.register(Font, true);
import { getToken } from "@/utils/auth";

export default {
  name: "Editor",
  props: {
    value: {
      type: String,
      default: "",
    },
    // 需要保留颜色的文字配置
    preserveColorTexts: {
      type: Array,
      default: () => [],
      // 格式: [{ text: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" }]
    },
    // 特定行的颜色配置 - 当用户粘贴内容到指定行时，自动应用颜色
    lineColorConfig: {
      type: Array,
      default: () => [],
      // 格式: [{ targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" }]
    },
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      uploadImgUrl: baseUrl + "/upload/file", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      quill: null,
      sourceCodeDialogVisible: false, // 源代码编辑弹窗显示状态
      sourceCodeContent: "", // 源代码内容
      options: {
        theme: "snow",
        modules: {
          toolbar: {
            container: [
              ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
              ["blockquote", "code-block"], // 引用  代码块
              //   [{ header: 1 }, { header: 2 }], // 1、2 级标题
              [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
              [{ script: "sub" }, { script: "super" }], // 上标/下标
              [{ indent: "-1" }, { indent: "+1" }], // 缩进
              // [{'direction': 'rtl'}],                         // 文本方向
              [{ size: ["small", false, "large", "huge"] }], // 字体大小
              [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
              [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
              [
                {
                  font: [
                    "SimSun",
                    "SimHei",
                    "Microsoft-YaHei",
                    "KaiTi",
                    "FangSong",
                    "Arial",
                    "Times-New-Roman",
                    "sans-serif",
                  ],
                },
              ], // 字体种类
              [{ align: [] }], // 对齐方式
              ["clean"], // 清除文本格式
              ["link", "image"], // 链接、图片、视频
              [
                { table: "TD" },
                { "table-insert-row": "TIR" },
                { "table-insert-column": "TIC" },
                { "table-delete-row": "TDR" },
                { "table-delete-column": "TDC" },
              ],
              ["source-code"], // 源代码编辑
            ],
            handlers: {
              image: function(value) {
                if (value) {
                  // 触发input框选择图片文件
                  document.querySelector(".quill-img input").click();
                } else {
                  this.quill.format("image", false);
                }
              },
              table: function() {
                this.quill.getModule("table").insertTable(3, 3);
              },
              "table-insert-row": function() {
                this.quill.getModule("table").insertRowBelow();
              },
              "table-insert-column": function() {
                this.quill.getModule("table").insertColumnRight();
              },
              "table-delete-row": function() {
                this.quill.getModule("table").deleteRow();
              },
              "table-delete-column": function() {
                this.quill.getModule("table").deleteColumn();
              },
              "source-code": () => {
                this.openSourceCodeDialog();
              },
            },
          },
          table: true,
        },
        // readOnly: true, //是否只读
        placeholder: "",
      },
    };
  },
  watch: {
    // 监听父组件传入的value变化
    value: {
      handler(newVal) {
        if (!this.quill) return;

        const currentContent = this.quill.root.innerHTML;

        // 避免重复设置相同内容，防止无限循环
        if (newVal === currentContent) return;

        // 处理空值情况：清空编辑器
        if (
          !newVal ||
          newVal === "" ||
          newVal === null ||
          newVal === undefined
        ) {
          // 使用setText方法清空内容，使用silent源避免触发text-change事件
          this.quill.setText("", "silent");
        } else {
          console.log("newVal", newVal);
          // 处理有内容的情况：设置HTML内容
          // 使用dangerouslyPasteHTML覆盖整个编辑器内容，使用silent源避免触发text-change事件
          this.quill.clipboard.dangerouslyPasteHTML(newVal, "silent");
        }
      },
      immediate: false,
    },
  },
  methods: {
    addQuillTitle() {
      const oToolBar = document.querySelector(".ql-toolbar");
      const aButton = oToolBar.querySelectorAll("button");
      const aSelect = oToolBar.querySelectorAll("select");
      aButton.forEach(function(item) {
        if (item.className === "ql-script") {
          item.value === "sub" ? (item.title = "下标") : (item.title = "上标");
        } else if (item.className === "ql-indent") {
          item.value === "+1"
            ? (item.title = "向右缩进")
            : (item.title = "向左缩进");
        } else {
          item.title = titleConfig[item.classList[0]];
        }
      });
      aSelect.forEach(function(item) {
        item.parentNode.title = titleConfig[item.classList[0]];
      });
    },
    getContentData() {
      return this.quill.getContents();
    },
    /**
     * class转style，适用于邮件等场景
     * 用法：this.fontClassToStyle(html)
     * 建议在需要发送邮件或导出时调用，不要影响编辑器内部内容
     */
    fontClassToStyle(html) {
      const fontMap = {
        "ql-font-SimSun": "font-family: SimSun, serif;",
        "ql-font-SimHei": "font-family: SimHei, sans-serif;",
        "ql-font-Microsoft-YaHei": "font-family: Microsoft YaHei, sans-serif;",
        "ql-font-KaiTi": "font-family: KaiTi, serif;",
        "ql-font-FangSong": "font-family: FangSong, serif;",
        "ql-font-Arial": "font-family: Arial, sans-serif;",
        "ql-font-Times-New-Roman": "font-family: Times New Roman, serif;",
        "ql-font-sans-serif": "font-family: sans-serif;",
      };
      return html.replace(
        /class="([^"]*?ql-font-[^"]*?)"/g,
        (match, classNames) => {
          let style = "";
          classNames.split(" ").forEach((cls) => {
            if (fontMap[cls]) style += fontMap[cls];
          });
          if (style) {
            // 保留原有class（如果有其他class），并加上style
            return match + ` style="${style}"`;
          }
          return match;
        }
      );
    },
    // 富文本图片上传前
    quillImgBefore(file) {
      const fileType = file.type;
      if (fileType === "image/jpeg" || fileType === "image/png") {
        return true;
      } else {
        this.$message.error("请插入图片类型文件(jpg/jpeg/png)");
        return false;
      }
    },

    quillImgSuccess(res) {
      // res为图片服务器返回的数据
      // 获取富文本组件实例
      // 如果上传成功
      if (res.code == 10000) {
        // 获取光标所在位置
        const length = this.quill.getSelection().index;
        // 插入图片  res.url为服务器返回的图片地址
        this.quill.insertEmbed(length, "image", res.data);
        // 调整光标到最后
        this.quill.setSelection(length + 1);
      } else {
        this.$message.error("图片插入失败");
      }
    },
    // 富文本图片上传失败
    uploadError() {
      // loading动画消失
      this.$message.error("图片插入失败");
    },

    // 打开源代码编辑弹窗
    openSourceCodeDialog() {
      // 获取当前编辑器的HTML内容
      this.sourceCodeContent = this.quill.root.innerHTML;
      this.sourceCodeDialogVisible = true;
    },

    // 确认源代码编辑
    confirmSourceCodeEdit() {
      try {
        // 将HTML内容设置到编辑器中，覆盖整个编辑器内容
        if (!this.sourceCodeContent || this.sourceCodeContent === "") {
          // 如果源代码为空，清空编辑器
          this.quill.setText("");
        } else {
          console.log("源代码", this.sourceCodeContent);
          // 使用dangerouslyPasteHTML覆盖整个编辑器内容
          this.quill.clipboard.dangerouslyPasteHTML(this.sourceCodeContent);
        }
        // 触发input事件，通知父组件内容变化
        this.$emit("input", this.quill.root.innerHTML);
        this.sourceCodeDialogVisible = false;
        this.$message.success("源代码应用成功");
      } catch (error) {
        this.$message.error("HTML代码格式错误，请检查后重试");
        console.error("HTML parsing error:", error);
      }
    },

    // 取消源代码编辑
    cancelSourceCodeEdit() {
      this.sourceCodeDialogVisible = false;
      // 重置源代码内容
      this.sourceCodeContent = "";
    },

    // 源代码弹窗关闭前的处理
    handleSourceCodeDialogClose(done) {
      this.$confirm("确认关闭？未保存的更改将丢失")
        .then(() => {
          this.cancelSourceCodeEdit();
          done();
        })
        .catch(() => {});
    },

    // 处理粘贴事件，保留特定文字的颜色和应用行颜色
    handlePasteWithColorPreservation(delta) {
      // 获取当前编辑器的HTML内容
      const currentContent = this.quill.root.innerHTML;

      // 处理保留特定文字颜色的逻辑
      const hasTargetText =
        this.preserveColorTexts &&
        this.preserveColorTexts.length > 0 &&
        this.preserveColorTexts.some((config) =>
          currentContent.includes(config.text)
        );

      // 处理特定行颜色应用的逻辑
      const hasLineColorConfig =
        this.lineColorConfig && this.lineColorConfig.length > 0;

      if (hasTargetText || hasLineColorConfig) {
        // 延迟执行，确保粘贴操作完成后再处理
        this.$nextTick(() => {
          setTimeout(() => {
            if (hasTargetText) {
              this.preserveTargetTextColors();
            }
            if (hasLineColorConfig) {
              this.applyLineColorAfterPaste();
            }
          }, 10);
        });
      }

      return delta;
    },

    // 保留目标文字的颜色
    preserveTargetTextColors() {
      try {
        let editorContent = this.quill.root.innerHTML;
        let contentChanged = false;

        // 遍历所有需要保留颜色的文字配置
        this.preserveColorTexts.forEach((config) => {
          const { text: targetText, color: targetColor } = config;

          // 使用正则表达式查找目标文字，不管它当前的样式如何
          const escapedText = targetText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
          const regex = new RegExp(`(<[^>]*>)*${escapedText}(<[^>]*>)*`, "gi");

          // 替换为带有指定颜色的版本
          const newContent = editorContent.replace(regex, (match) => {
            // 移除现有的颜色样式，然后添加新的颜色
            const cleanText = match.replace(/<[^>]*>/g, "");
            if (cleanText.includes(targetText)) {
              contentChanged = true;
              return `<span style="color: ${targetColor};">${targetText}</span>`;
            }
            return match;
          });

          editorContent = newContent;
        });

        // 如果内容发生了变化，更新编辑器
        if (contentChanged) {
          // 保存当前光标位置
          const selection = this.quill.getSelection();

          // 更新内容
          this.quill.clipboard.dangerouslyPasteHTML(editorContent, "silent");

          // 恢复光标位置
          if (selection) {
            this.$nextTick(() => {
              try {
                this.quill.setSelection(selection.index, selection.length);
              } catch (e) {
                // 如果无法恢复光标位置，设置到末尾
                this.quill.setSelection(this.quill.getLength());
              }
            });
          }

          // 触发内容变化事件
          this.$emit("input", editorContent);
        }
      } catch (error) {
        console.warn("保留文字颜色时出错:", error);
      }
    },

    // 在特定行粘贴后应用颜色
    applyLineColorAfterPaste() {
      try {
        let editorContent = this.quill.root.innerHTML;
        let contentChanged = false;

        // 遍历所有行颜色配置
        this.lineColorConfig.forEach((config) => {
          const { targetText, color } = config;

          // 查找包含目标文字的段落，并将整个段落的文字都设置为指定颜色
          const targetTextRegex = new RegExp(
            targetText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
            "gi"
          );

          // 按段落分割内容
          const paragraphs = editorContent.split(/(<p[^>]*>|<\/p>)/gi);

          for (let i = 0; i < paragraphs.length; i++) {
            const paragraph = paragraphs[i];

            // 如果段落包含目标文字
            if (paragraph && targetTextRegex.test(paragraph)) {
              // 将整个段落的所有文字都设置为指定颜色
              const styledParagraph = this.applyColorToParagraph(
                paragraph,
                color
              );
              paragraphs[i] = styledParagraph;
              contentChanged = true;
            }
          }

          if (contentChanged) {
            editorContent = paragraphs.join("");
          }
        });

        // 如果内容发生了变化，更新编辑器
        if (contentChanged) {
          // 保存当前光标位置
          const selection = this.quill.getSelection();

          // 更新内容
          this.quill.clipboard.dangerouslyPasteHTML(editorContent, "silent");

          // 恢复光标位置
          if (selection) {
            this.$nextTick(() => {
              try {
                this.quill.setSelection(selection.index, selection.length);
              } catch (e) {
                // 如果无法恢复光标位置，设置到末尾
                this.quill.setSelection(this.quill.getLength());
              }
            });
          }

          // 触发内容变化事件
          this.$emit("input", editorContent);
        }
      } catch (error) {
        console.warn("应用行颜色时出错:", error);
      }
    },

    // 为整个段落应用颜色
    applyColorToParagraph(paragraph, color) {
      // 移除所有现有的颜色样式，然后应用新的颜色
      let styledParagraph = paragraph;

      // 移除现有的 style 属性中的 color 设置
      styledParagraph = styledParagraph.replace(
        /style="[^"]*color:[^;"]*;?[^"]*"/gi,
        (match) => {
          const styleContent = match
            .replace(/color:[^;"]*;?/gi, "")
            .replace(/style=""/gi, "");
          return styleContent.includes('style=""') ? "" : styleContent;
        }
      );

      // 移除空的 span 标签
      styledParagraph = styledParagraph.replace(/<span[^>]*><\/span>/gi, "");

      // 为所有文本内容添加颜色
      styledParagraph = styledParagraph.replace(/>([^<]+)</g, (match, text) => {
        if (text.trim()) {
          return `><span style="color: ${color};">${text}</span><`;
        }
        return match;
      });

      // 处理段落开头的文本
      styledParagraph = styledParagraph.replace(/^([^<]+)/, (match, text) => {
        if (text.trim()) {
          return `<span style="color: ${color};">${text}</span>`;
        }
        return match;
      });

      return styledParagraph;
    },

    // 文本变化后应用行颜色（包括输入、粘贴、删除等所有用户操作）
    applyLineColorAfterTextChange() {
      try {
        let editorContent = this.quill.root.innerHTML;
        let contentChanged = false;

        // 遍历所有行颜色配置
        this.lineColorConfig.forEach((config) => {
          const { targetText, color } = config;

          // 查找包含目标文字的段落，或者曾经包含目标文字但现在被替换的段落
          const paragraphs = editorContent.split(/(<p[^>]*>|<\/p>)/gi);

          for (let i = 0; i < paragraphs.length; i++) {
            const paragraph = paragraphs[i];

            if (
              paragraph &&
              this.shouldApplyColorToParagraph(paragraph, targetText)
            ) {
              // 将整个段落的所有文字都设置为指定颜色
              const styledParagraph = this.applyColorToParagraph(
                paragraph,
                color
              );
              paragraphs[i] = styledParagraph;
              contentChanged = true;
            }
          }

          if (contentChanged) {
            editorContent = paragraphs.join("");
          }
        });

        // 如果内容发生了变化，更新编辑器
        if (contentChanged) {
          // 保存当前光标位置
          const selection = this.quill.getSelection();

          // 更新内容
          this.quill.clipboard.dangerouslyPasteHTML(editorContent, "silent");

          // 恢复光标位置
          if (selection) {
            this.$nextTick(() => {
              try {
                this.quill.setSelection(selection.index, selection.length);
              } catch (e) {
                // 如果无法恢复光标位置，设置到末尾
                this.quill.setSelection(this.quill.getLength());
              }
            });
          }

          // 触发内容变化事件
          this.$emit("input", editorContent);
        }
      } catch (error) {
        console.warn("文本变化后应用行颜色时出错:", error);
      }
    },

    // 判断段落是否应该应用颜色
    shouldApplyColorToParagraph(paragraph, targetText) {
      // 1. 段落包含目标文字
      const targetTextRegex = new RegExp(
        targetText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
        "gi"
      );
      if (targetTextRegex.test(paragraph)) {
        return true;
      }

      // 2. 段落中有未设置颜色的文字，且该段落可能是用户编辑的目标行
      // 检查段落是否包含没有颜色样式的文字
      const hasUnstyledText = this.hasUnstyledTextInParagraph(paragraph);

      // 检查段落是否在目标位置（通过上下文判断）
      const isTargetLine = this.isLikelyTargetLine(paragraph, targetText);

      return hasUnstyledText && isTargetLine;
    },

    // 检查段落中是否有未设置样式的文字
    hasUnstyledTextInParagraph(paragraph) {
      // 移除所有HTML标签，获取纯文本
      const textContent = paragraph.replace(/<[^>]*>/g, "").trim();

      // 如果没有文本内容，返回false
      if (!textContent) {
        return false;
      }

      // 检查是否有没有颜色样式的文字
      // 如果段落中有文字但没有color样式，认为是未设置样式的文字
      const hasColorStyle = /style="[^"]*color\s*:[^"]*"/i.test(paragraph);

      return !hasColorStyle && textContent.length > 0;
    },

    // 判断是否是可能的目标行（通过上下文和位置判断）
    isLikelyTargetLine(paragraph, targetText) {
      // 获取当前编辑器的所有内容
      const allContent = this.quill.root.innerHTML;

      // 查找目标文字在整个文档中的位置
      const targetTextRegex = new RegExp(
        targetText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
        "gi"
      );

      // 如果文档中还存在目标文字，说明当前段落不是被替换的目标行
      if (targetTextRegex.test(allContent)) {
        return false;
      }

      // 如果文档中不存在目标文字，且当前段落有内容，可能是被替换的目标行
      // 进一步检查：段落是否在合理的位置（比如在其他蓝色文字附近）
      const allParagraphs = allContent.split(/(<p[^>]*>|<\/p>)/gi);
      const currentIndex = allParagraphs.indexOf(paragraph);

      // 检查前后段落是否有蓝色文字，如果有，说明当前段落可能是目标行
      for (
        let i = Math.max(0, currentIndex - 2);
        i <= Math.min(allParagraphs.length - 1, currentIndex + 2);
        i++
      ) {
        if (i !== currentIndex && allParagraphs[i]) {
          const hasBlueText = /style="[^"]*color\s*:\s*rgb\(0,\s*0,\s*128\)/i.test(
            allParagraphs[i]
          );
          if (hasBlueText) {
            return true;
          }
        }
      }

      return false;
    },
  },
  mounted() {
    const dom = this.$el.querySelector(".editor");
    this.quill = new Quill(dom, this.options);

    // 初始化编辑器内容
    if (this.value) {
      // 使用dangerouslyPasteHTML覆盖整个编辑器内容
      this.quill.clipboard.dangerouslyPasteHTML(this.value, "silent");
    }

    // 监听内容变化
    this.quill.on("text-change", (_, __, source) => {
      const htmlContent = this.quill.root.innerHTML;
      this.$emit("input", htmlContent);

      // 如果是用户操作（非程序设置），检查是否需要应用行颜色
      if (
        source === "user" &&
        this.lineColorConfig &&
        this.lineColorConfig.length > 0
      ) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.applyLineColorAfterTextChange();
          }, 10);
        });
      }
    });

    // 监听粘贴事件，保留特定文字的颜色
    this.quill.clipboard.addMatcher(Node.ELEMENT_NODE, (_, delta) => {
      return this.handlePasteWithColorPreservation(delta);
    });

    // 设置表格按钮图标
    this.$el.querySelector(
      ".ql-table-insert-row"
    ).innerHTML = `<svg t="1591862376726" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6306" width="18" height="200"><path d="M500.8 604.779L267.307 371.392l-45.227 45.27 278.741 278.613L779.307 416.66l-45.248-45.248z" p-id="6307"></path></svg>`;
    this.$el.querySelector(
      ".ql-table-insert-column"
    ).innerHTML = `<svg t="1591862238963" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6509" width="18" height="200"><path d="M593.450667 512.128L360.064 278.613333l45.290667-45.226666 278.613333 278.762666L405.333333 790.613333l-45.226666-45.269333z" p-id="6510"></path></svg>`;
    this.$el.querySelector(
      ".ql-table-delete-row"
    ).innerHTML = `<svg t="1591862253524" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6632" width="18" height="200"><path d="M500.8 461.909333L267.306667 695.296l-45.226667-45.269333 278.741333-278.613334L779.306667 650.026667l-45.248 45.226666z" p-id="6633"></path></svg>`;
    this.$el.querySelector(
      ".ql-table-delete-column"
    ).innerHTML = `<svg t="1591862261059" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6755" width="18" height="200"><path d="M641.28 278.613333l-45.226667-45.226666-278.634666 278.762666 278.613333 278.485334 45.248-45.269334-233.365333-233.237333z" p-id="6756"></path></svg>`;

    // 设置源代码按钮图标和标题
    const sourceCodeBtn = this.$el.querySelector(".ql-source-code");
    if (sourceCodeBtn) {
      sourceCodeBtn.innerHTML = `<svg viewBox="0 0 1024 1024" width="18" height="18"><path d="M320 768L96 544l224-224 64 64-160 160 160 160-64 64z m384 0l-64-64 160-160-160-160 64-64 224 224-224 224z"></path></svg>`;
      sourceCodeBtn.title = "源代码编辑";
    }

    this.addQuillTitle();
  },
  activated() {
    this.quill.setContents({});
  },
};
</script>
<style lang="less">
.editor {
  line-height: normal !important;
  height: 192px;
}
/*.el-upload {*/
/*  display: none;*/
/*}*/

.ql-container {
  max-height: 160px;
  overflow: auto;
  .ql-tooltip {
    left: 0 !important;
  }
}
.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}
.ql-editor {
  /* overflow: hidden; */
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32px";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "标准字体";
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimSun"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimSun"]::before {
  content: "宋体";
  font-family: "SimSun", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimHei"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimHei"]::before {
  content: "黑体";
  font-family: "SimHei", sans-serif;
}
.ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value="Microsoft-YaHei"]::before,
.ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value="Microsoft-YaHei"]::before {
  content: "微软雅黑";
  font-family: "Microsoft YaHei", sans-serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="KaiTi"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="KaiTi"]::before {
  content: "楷体";
  font-family: "KaiTi", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="FangSong"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="FangSong"]::before {
  content: "仿宋";
  font-family: "FangSong", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Arial"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Arial"]::before {
  content: "Arial";
  font-family: "Arial", sans-serif;
}
.ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value="Times-New-Roman"]::before,
.ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value="Times-New-Roman"]::before {
  content: "Times New Roman";
  font-family: "Times New Roman", serif;
}

.ql-font-SimSun {
  font-family: "SimSun", serif;
}
.ql-font-SimHei {
  font-family: "SimHei", sans-serif;
}
.ql-font-Microsoft-YaHei {
  font-family: "Microsoft YaHei", sans-serif;
}
.ql-font-KaiTi {
  font-family: "KaiTi", serif;
}
.ql-font-FangSong {
  font-family: "FangSong", serif;
}
.ql-font-Arial {
  font-family: "Arial", sans-serif;
}
.ql-font-Times-New-Roman {
  font-family: "Times New Roman", serif;
}
.ql-font-sans-serif {
  font-family: sans-serif;
}

/* 源代码编辑相关样式 */
.source-code-editor {
  .source-code-textarea {
    font-family: "Courier New", Consolas, monospace;
    font-size: 14px;
    line-height: 1.5;

    /deep/ .el-textarea__inner {
      font-family: "Courier New", Consolas, monospace;
      font-size: 14px;
      line-height: 1.5;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 12px;
      resize: vertical;

      &:focus {
        border-color: #409eff;
        outline: none;
      }
    }
  }
}

/* 源代码按钮样式 */
.ql-toolbar .ql-source-code {
  width: 28px;
  height: 28px;

  svg {
    width: 18px;
    height: 18px;
    fill: #444;
  }

  &:hover svg {
    fill: #06c;
  }
}
</style>
