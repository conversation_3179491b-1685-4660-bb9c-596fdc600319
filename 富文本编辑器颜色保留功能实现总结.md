# 富文本编辑器颜色保留功能实现总结

## 需求回顾

在 `src/views/ledger/commonEmail/components/replyEmailDrawer.vue` 页面中，富文本编辑器默认的 `endTextHtml` 包含蓝色文字"（公司名称手动自定义）"（颜色值为 rgb(0, 0, 128)）。

**核心需求**：当用户在包含"（公司名称手动自定义）"的行中输入、粘贴或替换内容时，新输入的内容应该自动保持蓝色格式 rgb(0, 0, 128)。

## 实现方案

### 1. 核心功能增强

**文件：** `src/components/Editor/index.vue`

#### 新增 Props

```javascript
// 需要保留颜色的文字配置
preserveColorTexts: {
  type: Array,
  default: () => [],
  // 格式: [{ text: "致", color: "rgb(0, 0, 128)" }]
},
// 特定行的颜色配置 - 当用户在指定行输入或粘贴内容时，自动应用颜色
lineColorConfig: {
  type: Array,
  default: () => [],
  // 格式: [{ targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" }]
},
```

#### 事件监听

```javascript
// 监听粘贴事件
this.quill.clipboard.addMatcher(Node.ELEMENT_NODE, (_, delta) => {
  return this.handlePasteWithColorPreservation(delta);
});

// 监听文本变化事件（包括输入、粘贴、删除等所有用户操作）
this.quill.on("text-change", (_, __, source) => {
  const htmlContent = this.quill.root.innerHTML;
  this.$emit("input", htmlContent);
  
  // 如果是用户操作（非程序设置），检查是否需要应用行颜色
  if (source === "user" && this.lineColorConfig && this.lineColorConfig.length > 0) {
    this.$nextTick(() => {
      setTimeout(() => {
        this.applyLineColorAfterTextChange();
      }, 10);
    });
  }
});
```

#### 核心处理逻辑
- `handlePasteWithColorPreservation()`: 检测粘贴事件并触发颜色处理
- `preserveTargetTextColors()`: 执行特定文字的颜色保留
- `applyLineColorAfterTextChange()`: 在文本变化后应用行颜色
- `shouldApplyColorToParagraph()`: 判断段落是否应该应用颜色
- `isLikelyTargetLine()`: 通过上下文判断是否是目标行

### 2. 业务页面集成

**文件：** `src/views/ledger/commonEmail/components/replyEmailDrawer.vue`

#### 配置数据
```javascript
// 需要保留颜色的文字配置
preserveColorTexts: [
  { text: "致", color: "rgb(0, 0, 128)" },
  { text: "确认，感谢支持！", color: "rgb(0, 0, 128)" },
],
// 特定行颜色配置 - 当用户在包含目标文字的行中输入或粘贴内容时，自动应用颜色
lineColorConfig: [
  { targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" },
],
```

#### 组件使用
```vue
<Editor
  v-model="formParams.mailReplyContent"
  ref="quillEditor"
  class="email-editor"
  :preserveColorTexts="preserveColorTexts"
  :lineColorConfig="lineColorConfig"
/>
```

## 技术实现细节

### 1. 文本变化监听

```javascript
// 监听文本变化事件（包括输入、粘贴、删除等所有用户操作）
this.quill.on("text-change", (_, __, source) => {
  // 如果是用户操作（非程序设置），检查是否需要应用行颜色
  if (source === "user" && this.lineColorConfig && this.lineColorConfig.length > 0) {
    this.$nextTick(() => {
      setTimeout(() => {
        this.applyLineColorAfterTextChange();
      }, 10);
    });
  }
});
```

### 2. 行颜色应用逻辑

```javascript
applyLineColorAfterTextChange() {
  // 遍历所有行颜色配置
  this.lineColorConfig.forEach((config) => {
    const { targetText, color } = config;
    
    // 查找包含目标文字的段落，或者曾经包含目标文字但现在被替换的段落
    const paragraphs = editorContent.split(/(<p[^>]*>|<\/p>)/gi);
    
    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i];
      
      if (paragraph && this.shouldApplyColorToParagraph(paragraph, targetText)) {
        // 将整个段落的所有文字都设置为指定颜色
        const styledParagraph = this.applyColorToParagraph(paragraph, color);
        paragraphs[i] = styledParagraph;
        contentChanged = true;
      }
    }
  });
}
```

### 3. 智能识别目标行

```javascript
shouldApplyColorToParagraph(paragraph, targetText) {
  // 1. 段落包含目标文字
  const targetTextRegex = new RegExp(
    targetText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
    "gi"
  );
  if (targetTextRegex.test(paragraph)) {
    return true;
  }

  // 2. 段落中有未设置颜色的文字，且该段落可能是用户编辑的目标行
  const hasUnstyledText = this.hasUnstyledTextInParagraph(paragraph);
  const isTargetLine = this.isLikelyTargetLine(paragraph, targetText);

  return hasUnstyledText && isTargetLine;
}
```

### 4. 上下文判断

```javascript
isLikelyTargetLine(paragraph, targetText) {
  // 获取当前编辑器的所有内容
  const allContent = this.quill.root.innerHTML;
  
  // 如果文档中不存在目标文字，且当前段落有内容，可能是被替换的目标行
  // 检查前后段落是否有蓝色文字，如果有，说明当前段落可能是目标行
  const allParagraphs = allContent.split(/(<p[^>]*>|<\/p>)/gi);
  const currentIndex = allParagraphs.indexOf(paragraph);
  
  for (let i = Math.max(0, currentIndex - 2); i <= Math.min(allParagraphs.length - 1, currentIndex + 2); i++) {
    if (i !== currentIndex && allParagraphs[i]) {
      const hasBlueText = /style="[^"]*color\s*:\s*rgb\(0,\s*0,\s*128\)/i.test(allParagraphs[i]);
      if (hasBlueText) {
        return true;
      }
    }
  }

  return false;
}
```

## 功能特性

### 1. 全面的文本操作支持
- 支持用户直接输入文字时自动应用颜色
- 支持粘贴内容时自动应用颜色
- 支持删除后重新输入时自动应用颜色

### 2. 智能识别目标行
- 通过目标文字精确识别特定行
- 当目标文字被替换后，通过上下文判断目标行
- 考虑段落位置和周围内容进行智能判断

### 3. 完整的颜色处理
- 移除现有的颜色样式，应用新的颜色
- 处理复杂的HTML结构和嵌套标签
- 保持光标位置不变，提供流畅的编辑体验

### 4. 性能优化
- 使用延迟执行避免与用户操作冲突
- 只在必要时执行颜色处理逻辑
- 批量处理多个配置项

## 测试验证

创建了专门的测试组件：
- `src/views/demo/text-input-color-demo.vue`

测试场景：
1. 直接在包含"（公司名称手动自定义）"的行中输入新内容
2. 删除"（公司名称手动自定义）"并输入新内容
3. 复制文字并粘贴到该行
4. 在其他行输入内容（验证不会影响其他行）

## 总结

本次实现成功解决了富文本编辑器在特定行输入或粘贴内容时自动应用颜色的需求。通过监听文本变化事件和智能识别目标行，确保用户在替换"（公司名称手动自定义）"时，新输入的内容能够自动保持蓝色格式。实现方案技术可靠，用户体验良好，完全满足业务需求。
