# 富文本编辑器颜色保留功能说明

## 功能概述

为了满足在特定场景下保留文字颜色的需求，我们对富文本编辑器组件进行了增强，支持两种颜色处理模式：

1. **文字颜色保留**：在用户粘贴内容时自动保留指定文字的颜色样式
2. **行颜色应用**：当用户在包含特定文字的行中粘贴内容时，自动将粘贴的内容应用指定颜色

## 实现原理

### 文字颜色保留模式

1. **粘贴事件监听**：通过 Quill 编辑器的 `clipboard.addMatcher` 方法监听粘贴事件
2. **颜色配置化**：支持通过 props 传入需要保留颜色的文字配置
3. **智能识别**：在粘贴操作完成后，自动识别并恢复指定文字的颜色
4. **正则匹配**：使用正则表达式精确匹配目标文字，不受现有 HTML 标签影响

### 行颜色应用模式

1. **目标行识别**：识别包含特定文字的段落
2. **内容分析**：检测段落中的新粘贴内容
3. **颜色应用**：将段落中的所有文字应用指定颜色
4. **样式处理**：处理现有样式，确保颜色正确应用

## 使用方法

### 1. 文字颜色保留模式

```vue
<template>
  <Editor v-model="content" :preserveColorTexts="preserveColorTexts" />
</template>

<script>
export default {
  data() {
    return {
      content: "",
      preserveColorTexts: [
        { text: "致", color: "rgb(0, 0, 128)" },
        { text: "确认，感谢支持！", color: "rgb(0, 0, 128)" },
      ],
    };
  },
};
</script>
```

### 2. 行颜色应用模式

```vue
<template>
  <Editor v-model="content" :lineColorConfig="lineColorConfig" />
</template>

<script>
export default {
  data() {
    return {
      content: "",
      lineColorConfig: [
        { targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" },
      ],
    };
  },
};
</script>
```

### 3. 混合使用

```vue
<template>
  <Editor
    v-model="content"
    :preserveColorTexts="preserveColorTexts"
    :lineColorConfig="lineColorConfig"
  />
</template>

<script>
export default {
  data() {
    return {
      content: "",
      preserveColorTexts: [
        { text: "致", color: "rgb(0, 0, 128)" },
        { text: "确认，感谢支持！", color: "rgb(0, 0, 128)" },
      ],
      lineColorConfig: [
        { targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" },
      ],
    };
  },
};
</script>
```

### 4. 配置参数说明

#### preserveColorTexts (Array)

需要保留颜色的文字配置数组，每个配置项包含：

- `text` (String): 需要保留颜色的文字内容
- `color` (String): 要保留的颜色值，支持 rgb、hex、颜色名称等 CSS 颜色格式

**示例：**

```javascript
preserveColorTexts: [
  { text: "重要提示", color: "red" },
  { text: "致", color: "rgb(0, 0, 128)" },
  { text: "联系方式", color: "#ff6600" },
];
```

#### lineColorConfig (Array)

特定行颜色配置数组，当用户在包含目标文字的行中粘贴内容时，自动应用颜色。每个配置项包含：

- `targetText` (String): 目标文字，用于识别特定行
- `color` (String): 要应用的颜色值，支持 rgb、hex、颜色名称等 CSS 颜色格式

**示例：**

```javascript
lineColorConfig: [
  { targetText: "（公司名称手动自定义）", color: "rgb(0, 0, 128)" },
  { targetText: "请填写联系方式", color: "#ff6600" },
];
```

## 功能特性

### 1. 文字颜色保留模式

- 支持精确匹配指定文字
- 不受现有 HTML 标签和样式影响
- 自动处理特殊字符转义
- 在粘贴操作后自动恢复指定文字的颜色

### 2. 行颜色应用模式

- 自动识别包含目标文字的段落
- 将粘贴到特定行的内容自动应用颜色
- 保持段落中所有文字颜色一致
- 智能处理现有样式和标签

### 3. 通用特性

- 支持多种 CSS 颜色格式
- 保持光标位置不变
- 使用延迟执行避免与粘贴操作冲突
- 批量处理多个配置项
- 性能优化，只在必要时执行处理逻辑

## 应用场景

### 1. 邮件回复场景

在邮件回复功能中，默认模板包含蓝色的提示文字"（公司名称手动自定义）"，当用户复制公司名称并粘贴替换时，需要保持该行文字的蓝色样式。使用行颜色应用模式可以确保用户粘贴的公司名称自动变为蓝色。

### 2. 模板编辑场景

在各种文档模板中，某些关键信息需要保持特定的颜色标识，即使用户进行复制粘贴操作也不应该丢失这些样式。可以根据需要选择文字颜色保留模式或行颜色应用模式。

### 3. 表单填写场景

在表单的富文本字段中，某些提示性文字需要保持特定颜色，方便用户识别和操作。当用户在特定区域填写内容时，可以自动应用统一的颜色样式。

## 技术实现细节

### 1. 粘贴事件处理

```javascript
// 监听粘贴事件
this.quill.clipboard.addMatcher(Node.ELEMENT_NODE, (_, delta) => {
  return this.handlePasteWithColorPreservation(delta);
});
```

### 2. 文字颜色保留逻辑

```javascript
preserveTargetTextColors() {
  // 遍历所有需要保留颜色的文字配置
  this.preserveColorTexts.forEach(config => {
    const { text: targetText, color: targetColor } = config;

    // 使用正则表达式查找并替换
    const escapedText = targetText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(<[^>]*>)*${escapedText}(<[^>]*>)*`, "gi");

    // 替换为带有指定颜色的版本
    editorContent = editorContent.replace(regex, (match) => {
      const cleanText = match.replace(/<[^>]*>/g, "");
      if (cleanText.includes(targetText)) {
        return `<span style="color: ${targetColor};">${targetText}</span>`;
      }
      return match;
    });
  });
}
```

## 注意事项

1. **性能考虑**：建议不要配置过多的保留文字，以免影响编辑器性能
2. **文字精确性**：配置的文字必须与实际内容完全匹配（区分大小写）
3. **颜色格式**：建议使用标准的 CSS 颜色格式，如 rgb()、hex 等
4. **兼容性**：功能基于 Quill 编辑器实现，确保项目中使用的是兼容版本

## 测试验证

项目中提供了测试组件 `test-editor-color-preservation.vue`，可以用于验证功能是否正常工作：

1. 在测试页面中观察预设的蓝色文字
2. 复制任意文字并粘贴到蓝色文字位置
3. 验证粘贴后的文字是否保持了蓝色样式
4. 测试多个不同的目标文字

## 更新日志

- **v1.0.0** (2025-01-21): 初始版本，支持基本的文字颜色保留功能
  - 添加 `preserveColorTexts` 配置项
  - 实现粘贴事件监听和颜色保留逻辑
  - 支持多个文字的批量颜色保留
  - 添加测试组件和使用文档
