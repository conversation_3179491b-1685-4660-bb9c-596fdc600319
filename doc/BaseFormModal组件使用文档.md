# BaseFormModal 组件使用文档

## 概述

BaseFormModal 是一个基于 Element UI 的弹窗表单组件，封装了常用的弹窗表单功能，支持动态表单配置、表单验证、自定义插槽等特性。

## 组件位置

```
src/components/GridTable/BaseFormModal/index.vue
```

## 基本用法

### 1. 引入组件

```javascript
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";

export default {
  components: { BaseFormModal },
  // ...
}
```

### 2. 模板使用

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button @click="openModal">打开弹窗</el-button>
    
    <!-- BaseFormModal 组件 -->
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      :config="modalConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
      modalWidth="50%"
      labelWidth="120px"
    />
  </div>
</template>
```

### 3. 基本配置

```javascript
export default {
  data() {
    return {
      // 其他数据...
    };
  },
  computed: {
    modalConfig() {
      return {
        modalTitle: "选择下载模板",
        formConfig: [
          {
            field: "templateType",
            title: "模板类型",
            element: "el-radio-group",
            props: {
              options: [
                { value: "normal", label: "常规模板" },
                { value: "byOrderType", label: "按工单类型下载模板" }
              ]
            },
            defaultValue: "normal",
            rules: [{ required: true, message: "请选择模板类型" }]
          }
        ]
      };
    }
  },
  methods: {
    openModal() {
      this.$refs.formModal.open({
        templateType: "normal"
      });
    },
    modalConfirmHandler(formData) {
      console.log("表单数据:", formData);
      // 处理表单提交逻辑
    }
  }
}
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modalTitle | String | "" | 弹窗标题 |
| modalWidth | String | "50%" | 弹窗宽度 |
| config | Array | [] | 表单配置数组 |
| showForm | Boolean | true | 是否显示表单 |
| autoClose | Boolean | true | 提交后是否自动关闭弹窗 |
| loading | Boolean | false | 提交按钮加载状态 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| modalConfirm | formParams | 表单提交时触发，参数为表单数据对象 |

## 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| open | row | 打开弹窗，参数为初始表单数据 |
| closeVisible | - | 关闭弹窗 |
| setFormFields | values | 设置表单字段值 |
| getFormFields | - | 获取当前表单数据 |

## 表单配置 (config)

表单配置是一个数组，每个元素代表一个表单项的配置：

### 基本字段配置

```javascript
{
  field: "fieldName",        // 字段名（必填）
  title: "字段标题",          // 字段标签（必填）
  element: "el-input",       // 使用的 Element UI 组件
  defaultValue: "",          // 默认值
  rules: [],                 // 验证规则
  props: {},                 // 传递给组件的属性
  attrs: {},                 // 传递给组件的 HTML 属性
  on: {},                    // 事件监听器
  show: true,                // 是否显示该字段
  slotName: "customSlot"     // 自定义插槽名
}
```

### 常用表单元素示例

#### 1. 输入框

```javascript
{
  field: "name",
  title: "名称",
  element: "el-input",
  rules: [{ required: true, message: "请输入名称" }],
  attrs: {
    placeholder: "请输入名称",
    maxlength: 50
  }
}
```

#### 2. 下拉选择

```javascript
{
  field: "status",
  title: "状态",
  element: "el-select",
  props: {
    options: [
      { value: "1", label: "启用" },
      { value: "0", label: "停用" }
    ]
  },
  defaultValue: "1"
}
```

#### 3. 单选框组

```javascript
{
  field: "type",
  title: "类型",
  element: "el-radio-group",
  props: {
    options: [
      { value: "A", label: "类型A" },
      { value: "B", label: "类型B" }
    ]
  },
  defaultValue: "A"
}
```

#### 4. 级联选择器

```javascript
{
  field: "orderType",
  title: "工单类型",
  element: "el-cascader",
  props: {
    options: this.orderTypeOptions,
    props: {
      checkStrictly: false,
      multiple: true,
      value: "id",
      label: "typeName",
      children: "childrenList"
    },
    filterable: true
  }
}
```

#### 5. 文本域

```javascript
{
  field: "description",
  title: "描述",
  element: "el-input",
  props: {
    type: "textarea"
  },
  attrs: {
    rows: 4,
    maxlength: 500,
    showWordLimit: true
  }
}
```

## 高级用法

### 1. 条件显示字段

```javascript
{
  field: "orderType",
  title: "工单类型",
  element: "el-cascader",
  show: this.formParams.templateType === "byOrderType", // 条件显示
  rules: [
    {
      required: this.formParams.templateType === "byOrderType",
      message: "请选择工单类型"
    }
  ]
}
```

### 2. 自定义插槽

```vue
<template>
  <BaseFormModal
    ref="formModal"
    :modalTitle="modalConfig.modalTitle"
    :config="modalConfig.formConfig"
    @modalConfirm="modalConfirmHandler"
  >
    <!-- 自定义插槽 -->
    <template #customSlot="{ item, params }">
      <div>
        <el-input v-model="params[item.field]" />
        <span>自定义内容</span>
      </div>
    </template>
  </BaseFormModal>
</template>
```

### 3. 表单验证

```javascript
rules: [
  { required: true, message: "此字段为必填项", trigger: "blur" },
  { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" },
  { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" },
  {
    validator: (rule, value, callback) => {
      if (value && value < 0) {
        callback(new Error("数值不能小于0"));
      } else {
        callback();
      }
    },
    trigger: "blur"
  }
]
```

## 完整示例

以下是一个完整的模板下载弹窗示例：

```vue
<template>
  <div>
    <el-button @click="openTemplateModal">下载模板</el-button>
    
    <BaseFormModal
      ref="templateModal"
      :modalTitle="templateModalConfig.modalTitle"
      :config="templateModalConfig.formConfig"
      @modalConfirm="handleTemplateDownload"
      modalWidth="50%"
      labelWidth="140px"
    />
  </div>
</template>

<script>
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import { queryDeptOrderTree, queryBusinessTypeTree } from "@/api/ledger/businessType.js";

export default {
  components: { BaseFormModal },
  data() {
    return {
      orderTypeOptions: [],
      businessTypeOptions: []
    };
  },
  computed: {
    templateModalConfig() {
      return {
        modalTitle: "选择下载模板",
        formConfig: [
          {
            field: "templateType",
            title: "模板类型",
            element: "el-radio-group",
            props: {
              options: [
                { value: "normal", label: "常规模板" },
                { value: "byOrderType", label: "按工单类型下载模板" }
              ]
            },
            defaultValue: "normal",
            rules: [{ required: true, message: "请选择模板类型" }]
          },
          {
            field: "orderType",
            title: "工单类型",
            element: "el-cascader",
            show: this.$refs.templateModal?.getFormFields()?.templateType === "byOrderType",
            props: {
              options: this.orderTypeOptions,
              props: {
                checkStrictly: false,
                multiple: false,
                value: "id",
                label: "typeName",
                children: "childrenList"
              },
              filterable: true
            },
            rules: [
              {
                required: this.$refs.templateModal?.getFormFields()?.templateType === "byOrderType",
                message: "请选择工单类型"
              }
            ]
          }
        ]
      };
    }
  },
  created() {
    this.loadOrderTypeOptions();
  },
  methods: {
    loadOrderTypeOptions() {
      queryDeptOrderTree({}).then((res) => {
        this.orderTypeOptions = res.data?.map((x) => ({ ...x }));
      });
    },
    openTemplateModal() {
      this.$refs.templateModal.open({
        templateType: "normal"
      });
    },
    handleTemplateDownload(formData) {
      if (formData.templateType === "normal") {
        // 下载常规模板
        this.downloadNormalTemplate();
      } else {
        // 按工单类型下载模板
        this.downloadTemplateByOrderType(formData.orderType);
      }
    },
    downloadNormalTemplate() {
      // 常规模板下载逻辑
      window.location.href = "/charging-maintenance-ui/static/工单台账导入模板.xlsx";
    },
    async downloadTemplateByOrderType(orderType) {
      // 按工单类型下载模板逻辑
      try {
        const res = await this.downLoadUrl2Blob({ 
          orderType: orderType 
        });
        if (res) {
          await this.fileDownLoad(res, `工单模板_${orderType}.xlsx`);
        }
      } catch (error) {
        this.$message.error("下载失败");
      }
    }
  }
}
</script>
```

## 注意事项

1. **表单验证**: 组件会自动进行表单验证，只有验证通过才会触发 `modalConfirm` 事件
2. **数据绑定**: 表单数据通过 `formParams` 进行双向绑定
3. **插槽使用**: 自定义插槽可以访问 `item`（字段配置）和 `params`（表单数据）
4. **异步操作**: 如果需要异步提交，可以设置 `autoClose: false` 并手动控制弹窗关闭
5. **响应式配置**: 表单配置支持响应式，可以根据数据变化动态调整表单结构

## 相关文档

- [DynamicForm 组件文档](./DynamicForm组件使用文档.md)
- [BuseCrud 组件使用指南](./BusCrud组件使用指南.md)
- [表单验证规范](./表单验证规范.md)
